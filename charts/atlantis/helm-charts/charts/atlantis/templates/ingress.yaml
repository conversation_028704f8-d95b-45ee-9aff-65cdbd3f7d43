{{- if .Values.ingress.enabled -}}
{{- $apiVersion := "extensions/v1beta1" -}}
{{- if .Values.ingress.apiVersion -}}
{{- $apiVersion = .Values.ingress.apiVersion -}}
{{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1/Ingress" -}}
{{- $apiVersion = "networking.k8s.io/v1" -}}
{{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta/Ingress" -}}
{{- $apiVersion = "networking.k8s.io/v1beta" -}}
{{- end -}}
{{- $fullName := include "atlantis.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $pathType := .Values.ingress.pathType -}}
apiVersion: {{ $apiVersion }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
{{- include "atlantis.labels" . | nindent 4 }}
{{- if .Values.ingress.labels }}
{{ toYaml .Values.ingress.labels | indent 4 }}
{{- end }}
{{- with .Values.ingress.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
{{- if .Values.ingress.ingressClassName }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
{{- end }}
{{- if .Values.ingress.tls }}
  tls:
{{ toYaml .Values.ingress.tls | indent 4 }}
{{- end }}
  rules:
    {{- if not .Values.ingress.hosts }}
    -
      {{- if .Values.ingress.host }}
      host: {{ .Values.ingress.host | quote }}
      {{- end }}
      http:
        paths:
        {{- if .Values.ingress.paths }}
          {{- range .Values.ingress.paths }}
          - path: {{ .path }}
            backend:
            {{- if eq $apiVersion "networking.k8s.io/v1" }}
              service:
                name: {{ .service }}
                port:
                  number: {{ .port }}
            pathType: {{ $.Values.ingress.pathType }}
            {{- else }}
              serviceName: {{ .service }}
              servicePort: {{ .port }}
            {{- end }}
          {{- end }}
        {{ else }}
          - path: {{ .Values.ingress.path }}
            backend:
            {{- if eq $apiVersion "networking.k8s.io/v1" }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ .Values.service.port }}
            pathType: {{ .Values.ingress.pathType }}
            {{ else }}
              serviceName: {{ $fullName }}
              servicePort: {{ .Values.service.port }}
            {{- end }}
        {{- end }}
    {{ else }}
    {{- range $k := .Values.ingress.hosts }}
    -
      {{- if .host }}
      host: {{ .host | quote }}
      {{- end }}
      http:
        paths:
        {{- range .paths }}
          - path: {{ . }}
            backend:
              {{- if eq $apiVersion "networking.k8s.io/v1" }}
              service:
                {{- if $k.service }}
                name: {{ $k.service }}
                {{- else }}
                name: {{ $fullName }}
                {{- end }}
                port:
                  number: {{ $svcPort }}
            pathType: {{ $pathType }}
              {{- else -}}
                {{- if $k.service }}
              serviceName: {{ $k.service }}
                {{- else }}
              serviceName: {{ $fullName }}
                {{- end }}
              servicePort: {{ $svcPort }}
              {{- end }}
        {{- end }}
    {{- end }}
    {{- end }}
{{- end }}
