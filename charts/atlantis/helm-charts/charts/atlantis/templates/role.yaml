{{- if .Values.enableKubernetesBackend -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "atlantis.fullname" . }}
  labels:
{{- include "atlantis.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - get
      - create
      - update
      - delete
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs: ['*']
{{- end -}}
