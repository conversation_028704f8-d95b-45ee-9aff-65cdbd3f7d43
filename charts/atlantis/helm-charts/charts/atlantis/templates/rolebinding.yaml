{{- if .Values.enableKubernetesBackend -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "atlantis.fullname" . }}
  labels:
{{- include "atlantis.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    name: {{ template "atlantis.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "atlantis.fullname" . }}
{{- end -}}
