{{- if not .Values.vcsSecretName }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "atlantis.fullname" . }}-webhook
  labels:
{{- include "atlantis.labels" . | nindent 4 }}
data:
  {{- if .Values.githubApp }}
  key.pem: {{ required "githubApp.key is required if githubApp configuration is specified." .Values.githubApp.key | b64enc }}
  github_secret: {{ required "githubApp.secret is required if githubApp configuration is specified." .Values.githubApp.secret | b64enc }}
  {{- end}}
  {{- if .Values.github.user }}
  github_token: {{ required "github.token is required if github configuration is specified." .Values.github.token | b64enc }}
  github_secret: {{ required "github.secret is required if github configuration is specified." .Values.github.secret | b64enc }}
  {{- end}}
  {{- if .Values.gitlab }}
  gitlab_token: {{ required "gitlab.token is required if gitlab configuration is specified." .Values.gitlab.token | b64enc }}
  gitlab_secret: {{ required "gitlab.secret is required if gitlab configuration is specified." .Values.gitlab.secret | b64enc }}
  {{- end}}
  {{- if .Values.azuredevops }}
  azuredevops_token: {{ required "azuredevops.token is required if azuredevops configuration is specified." .Values.azuredevops.token | b64enc }}
  azuredevops_webhook_password: {{ required "azuredevops.webhookPassword is required if azuredevops configuration is specified." .Values.azuredevops.webhookPassword | b64enc }}
  {{- end}}
{{- end }}
