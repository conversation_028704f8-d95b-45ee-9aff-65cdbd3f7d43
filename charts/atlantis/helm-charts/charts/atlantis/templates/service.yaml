apiVersion: v1
kind: Service
metadata:
  name: {{ template "atlantis.fullname" . }}
  labels:
{{- include "atlantis.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
  {{- end }}
spec:
{{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
{{ toYaml .Values.service.loadBalancerSourceRanges | indent 4 }}
{{- end }}
{{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
{{- end }}
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: atlantis
  selector:
    app: {{ template "atlantis.name" . }}
    release: {{ .Release.Name }}
