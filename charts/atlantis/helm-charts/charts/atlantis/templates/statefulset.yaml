apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "atlantis.fullname" . }}
  labels:
{{- include "atlantis.labels" . | nindent 4 }}
{{- if .Values.statefulSet.labels }}
{{ toYaml .Values.statefulSet.labels | indent 4 }}
{{- end }}
  {{- with .Values.statefulSet.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
  {{- end }}
spec:
  serviceName: {{ template "atlantis.fullname" . }}
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "atlantis.name" . }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ template "atlantis.name" . }}
        release: {{ .Release.Name }}
{{- if .Values.podTemplate.labels }}
{{ toYaml .Values.podTemplate.labels | indent 8 }}
{{- end }}
      annotations:
{{- if .Values.statefulset.annotations }}
{{ toYaml .Values.statefulset.annotations | indent 8 }}
{{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap-config.yaml") . | sha256sum }}
        checksum/repo-config: {{ include (print $.Template.BasePath "/configmap-repo-config.yaml") . | sha256sum }}
    spec:
      {{- if .Values.hostAliases }}
      hostAliases:
      {{- range .Values.hostAliases }}
        - hostnames: {{- range .hostnames }}
          - {{ . }}{{- end }}
          ip: {{ .ip }}
      {{- end }}
      {{- end }}
      serviceAccountName: {{ template "atlantis.serviceAccountName" . }}
      shareProcessNamespace: {{ .Values.statefulSet.shareProcessNamespace }}
      automountServiceAccountToken: {{ .Values.serviceAccount.mount }}
      {{- if .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      {{- end }}
      securityContext: {{ .Values.statefulSet.securityContext | toYaml | nindent 8 }}
      {{- if .Values.statefulSet.priorityClassName }}
      priorityClassName: {{ .Values.statefulSet.priorityClassName }}
      {{- end }}
      volumes:
      {{- if .Values.tlsSecretName }}
      - name: tls
        secret:
          secretName: {{ .Values.tlsSecretName }}
      {{- end }}
      {{- range $name, $_ := .Values.serviceAccountSecrets }}
      - name: {{ $name }}-volume
        secret:
          secretName: {{ $name }}
      {{- end }}
      {{- range .Values.googleServiceAccountSecrets }}
      - name: {{ .name }}
        secret:
          secretName: {{ .secretName }}
      {{- end }}
      {{- if .Values.gitconfig }}
      - name: gitconfig-volume
        secret:
          secretName: {{ template "atlantis.fullname" . }}-gitconfig
      {{- else if .Values.gitconfigSecretName }}
      - name: gitconfig-volume
        secret:
          secretName: {{ .Values.gitconfigSecretName }}
      {{- end }}
      {{- if .Values.netrc }}
      - name: netrc-volume
        secret:
          secretName: {{ template "atlantis.fullname" . }}-netrc
      {{- else if .Values.netrcSecretName }}
      - name: netrc-volume
        secret:
          secretName: {{ .Values.netrcSecretName }}
      {{- end }}
      {{- if or .Values.aws.credentials .Values.aws.config .Values.awsSecretName }}
      - name: aws-volume
        secret:
          secretName: {{ template "atlantis.awsSecretName" . }}
      {{- end }}
      {{- if .Values.githubApp }}
      {{- if or .Values.githubApp.key .Values.vcsSecretName}}
      - name: github-app-key-volume
        secret:
          secretName: {{ template "atlantis.vcsSecretName" . }}
          items:
          - key: key.pem
            path: key.pem
      {{- end }}
      {{- end }}
      {{- if .Values.repoConfig }}
      - name: repo-config
        configMap:
          name:  {{ template "atlantis.fullname" . }}-repo-config
      {{- end }}
      {{- if .Values.config }}
      - name: config
        configMap:
          name:  {{ template "atlantis.fullname" . }}
      {{- end }}
      {{- if .Values.containerSecurityContext.readOnlyRootFilesystem }}
      - name: atlantis-home-dir
        emptyDir: {}
      - name: tmp-dir
        emptyDir: {}
      {{- end }}
      {{- if .Values.customPem }}
      - name: additional-trust-certs
        secret:
          secretName: {{ .Values.customPem }}
      {{- end }}
      {{- if .Values.extraVolumes }}
{{ toYaml .Values.extraVolumes | indent 6 }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
      {{- range .Values.imagePullSecrets }}
      - name: {{ . }}
      {{- end }}
      {{- end }}
      {{- if .Values.initContainers }}
      initContainers:
{{ toYaml .Values.initContainers | indent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.containerSecurityContext }}
          securityContext: {{- toYaml .Values.containerSecurityContext | nindent 12 }}
          {{- end }}
          command: ['/bin/sh']
          args: ["-c", "source /vault/secrets/config && echo \"$SSH_BITBUCKET_PRIVATE_KEY\" >> ~/.ssh/id_rsa && docker-entrypoint.sh server --config=/etc/atlantis/atlantis.yaml"]
          ports:
          - name: atlantis
            containerPort: 4141
          {{- with .Values.lifecycle }}
          lifecycle: {{ toYaml . | nindent 12 }}
          {{- end }}
          {{- if or .Values.loadEnvFromSecrets .Values.loadEnvFromConfigMaps }}
          envFrom:
          {{- range .Values.loadEnvFromSecrets }}
            - secretRef:
                name: {{ . }}
          {{- end }}
          {{- range .Values.loadEnvFromConfigMaps }}
            - configMapRef:
                name: {{ . }}
          {{- end }}
          {{- end }}
          env:
          - name: GIT_SSH_COMMAND
            value: ssh -i $ATLANTIS_SSH_KEY -o 'StrictHostKeyChecking no'
          - name: ATLANTIS_SSH_KEY
            value: ~/.ssh/id_rsa
          - name: ATLANTIS_AUTOPLAN_MODULES
            value: "true"
          {{- if .Values.environmentRaw }}
          {{- with .Values.environmentRaw }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- end }}
          {{- range $key, $value := .Values.environment }}
          - name: {{ $key }}
            value: {{ $value | quote }}
          {{- end }}
          {{- range .Values.environmentSecrets }}
          - name: {{ .name }}
            valueFrom:
              secretKeyRef:
                name: {{ .secretKeyRef.name }}
                key: {{ .secretKeyRef.key }}
          {{- end }}
          {{- if .Values.allowForkPRs }}
          - name: ATLANTIS_ALLOW_FORK_PRS
            value: {{ .Values.allowForkPRs | quote }}
          {{- end }}
          {{- if .Values.allowDraftPRs }}
          - name: ATLANTIS_ALLOW_DRAFT_PRS
            value: {{ .Values.allowDraftPRs | quote }}
          {{- end }}
          {{- if .Values.hidePrevPlanComments }}
          - name: ATLANTIS_HIDE_PREV_PLAN_COMMENTS
            value: {{ .Values.hidePrevPlanComments | quote }}
          {{- end }}
          {{- if .Values.disableApply }}
          - name: ATLANTIS_DISABLE_APPLY
            value: {{ .Values.disableApply | quote }}
          {{- end }}
          {{- if .Values.disableApplyAll }}
          - name: ATLANTIS_DISABLE_APPLY_ALL
            value: {{ .Values.disableApplyAll | quote }}
          {{- end }}
          {{- if .Values.disableRepoLocking }}
          - name: ATLANTIS_DISABLE_REPO_LOCKING
            value: {{ .Values.disableRepoLocking | quote }}
          {{- end }}
          {{- if .Values.enableDiffMarkdownFormat }}
          - name: ATLANTIS_ENABLE_DIFF_MARKDOWN_FORMAT
            value: {{ .Values.enableDiffMarkdownFormat | quote }}
          {{- end }}
          {{- if .Values.defaultTFVersion }}
          - name: ATLANTIS_DEFAULT_TF_VERSION
            value: {{ .Values.defaultTFVersion }}
          {{- end }}
          {{- if .Values.logLevel }}
          - name: ATLANTIS_LOG_LEVEL
            value: {{ .Values.logLevel | quote}}
          {{- end }}
          {{- if .Values.tlsSecretName }}
          - name: ATLANTIS_SSL_CERT_FILE
            value: /etc/tls/tls.crt
          - name: ATLANTIS_SSL_KEY_FILE
            value: /etc/tls/tls.key
          {{- end }}
          - name: ATLANTIS_DATA_DIR
            value: /atlantis-data
          - name: ATLANTIS_REPO_ALLOWLIST
            value: {{ toYaml (coalesce .Values.orgWhitelist .Values.orgAllowlist) }}
          - name: ATLANTIS_PORT
            value: "4141"
          {{- if .Values.repoConfig }}
          - name: ATLANTIS_REPO_CONFIG
            value: /etc/atlantis/repos.yaml
          {{- end }}
          {{- if .Values.atlantisUrl }}
          - name: ATLANTIS_ATLANTIS_URL
            value: {{ .Values.atlantisUrl }}
          {{- else if .Values.ingress.enabled }}
          - name: ATLANTIS_ATLANTIS_URL
            {{- if .Values.ingress.tls }}
            value: https://{{ .Values.ingress.host }}
            {{- else }}
            value: http://{{ .Values.ingress.host }}
            {{- end }}
          {{- end }}
          {{- if .Values.bitbucket }}
          - name: ATLANTIS_BITBUCKET_USER
            value: eyecue-deployer
          # - name: ATLANTIS_BITBUCKET_TOKEN
          #   valueFrom:
          #     secretKeyRef:
          #       name: {{ template "atlantis.vcsSecretName" . }}
          #       key: bitbucket_token
          {{- if .Values.bitbucket.baseURL }}
          - name: ATLANTIS_BITBUCKET_BASE_URL
            value: {{ .Values.bitbucket.baseURL }}
          - name: ATLANTIS_BITBUCKET_WEBHOOK_SECRET
            valueFrom:
              secretKeyRef:
                name: {{ template "atlantis.vcsSecretName" . }}
                key: bitbucket_secret
          {{- end }}
          {{- end }}
          {{- if .Values.aws.directory }}
          - name: AWS_SHARED_CREDENTIALS_FILE
            value: {{ .Values.aws.directory }}/credentials
          - name: AWS_CONFIG_FILE
            value: {{ .Values.aws.directory }}/config
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /healthz
              port: 4141
              scheme: {{ .Values.livenessProbe.scheme }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /healthz
              port: 4141
              scheme: {{ .Values.readinessProbe.scheme }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          volumeMounts:
          {{- if or .Values.volumeClaim.enabled .Values.dataStorage  }}
          - name: atlantis-data
            mountPath: /atlantis-data
          {{- end }}
          {{- range $name, $_ := .Values.serviceAccountSecrets }}
          - name: {{ $name }}-volume
            readOnly: true
            mountPath: /etc/{{ $name }}
          {{- end }}
          {{- range .Values.googleServiceAccountSecrets }}
          - name: {{ .name }}
            readOnly: true
            mountPath: /var/secrets/{{ .name }}
          {{- end }}
          {{- if or .Values.gitconfig .Values.gitconfigSecretName }}
          - name: gitconfig-volume
            readOnly: true
            mountPath: /home/<USER>/.gitconfig
            subPath: gitconfig
          {{- end }}
          {{- if or .Values.netrc .Values.netrcSecretName }}
          - name: netrc-volume
            readOnly: true
            mountPath: /home/<USER>/.netrc
            subPath: netrc
          {{- end }}
          {{- if or .Values.aws.credentials .Values.aws.config .Values.awsSecretName }}
          - name: aws-volume
            readOnly: true
            mountPath: {{ .Values.aws.directory | default "/home/<USER>/.aws" }}
          {{- end }}
          {{- if .Values.tlsSecretName }}
          - name: tls
            mountPath: /etc/tls/
          {{- end }}
          {{- if .Values.repoConfig }}
          - name: repo-config
            mountPath: /etc/atlantis/repos.yaml
            subPath: repos.yaml
            readOnly: true
          {{- end }}
          {{- if .Values.githubApp }}
          {{- if or .Values.githubApp.key .Values.vcsSecretName}}
          - name: github-app-key-volume
            mountPath: /var/github-app
            readOnly: true
          {{- end }}
          {{- end }}
          {{- if .Values.config }}
          - name: config
            mountPath: /etc/atlantis/atlantis.yaml
            subPath: atlantis.yaml
            readOnly: true
          {{- end }}
          {{- if .Values.containerSecurityContext.readOnlyRootFilesystem }}
          - name: atlantis-home-dir
            mountPath: /home/<USER>
          - name: tmp-dir
            mountPath: /tmp
          {{- end }}
          {{- if .Values.customPem }}
          - name: additional-trust-certs
            mountPath: /etc/ssl/certs/ca-certificates.crt
            subPath: ca-certificates.crt
          {{- end }}
          {{- if .Values.extraVolumeMounts }}
{{ toYaml .Values.extraVolumeMounts | indent 10 }}
          {{- end }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
      {{- with .Values.extraContainers }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
  {{- with .Values.statefulSet.updateStrategy }}
  updateStrategy:
{{ toYaml . | indent 4 }}
  {{- end }}
  {{- if .Values.dataStorage }}
  volumeClaimTemplates:
  - metadata:
      name: atlantis-data
    spec:
      accessModes: ["ReadWriteOnce"] # Volume should not be shared by multiple nodes.
      {{- if .Values.storageClassName }}
      storageClassName: {{ .Values.storageClassName }} # Storage class of the volume
      {{- end }}
      resources:
        requests:
          # The biggest thing Atlantis stores is the Git repo when it checks it out.
          # It deletes the repo after the pull request is merged.
          storage: {{ .Values.dataStorage }}
  {{- else if .Values.volumeClaim.enabled }}
  volumeClaimTemplates:
  - metadata:
      name: atlantis-data
    spec:
      accessModes: ["ReadWriteOnce"] # Volume should not be shared by multiple nodes.
      {{- if .Values.volumeClaim.storageClassName }}
      storageClassName: {{ .Values.volumeClaim.storageClassName }} # Storage class of the volume
      {{- end }}
      resources:
        requests:
          # The biggest thing Atlantis stores is the Git repo when it checks it out.
          # It deletes the repo after the pull request is merged.
          storage: {{ .Values.volumeClaim.dataStorage }}
  {{- end }}
