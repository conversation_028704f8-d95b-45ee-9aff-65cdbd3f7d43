{{- if .Values.test.enabled -}}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ .Release.Name }}-ui-test-{{ randAlphaNum 5 | lower }}"
  annotations:
    "helm.sh/hook": test-success
spec:
  initContainers:
    - name: test-framework
      image: dduportal/bats:0.4.0
      command:
      - "bash"
      - "-c"
      - |
        set -ex
        # copy bats to tools dir
        cp -R /usr/local/libexec/ /tools/bats/
      volumeMounts:
      - mountPath: /tools
        name: tools
  containers:
    - name: {{ .Release.Name }}-ui-test
      image: {{ .Values.test.image }}:{{ .Values.test.imageTag }}
      command: ["/tools/bats/bats", "-t", "/tests/run.sh"]
      volumeMounts:
      - mountPath: /tests
        name: tests
        readOnly: true
      - mountPath: /tools
        name: tools
  volumes:
  - name: tests
    configMap:
      name: {{ template "atlantis.fullname" . }}-tests
  - name: tools
    emptyDir: {}
  restartPolicy: Never
{{- end }}
