## -------------------------- ##
# Values to override for your instance.
## -------------------------- ##

## An option to override the atlantis url,
##   if not using an ingress, set it to the external IP.
# atlantisUrl: http://10.0.0.0

# Replace this with your own repo allowlist:
orgAllowlist: <replace-me>
# logLevel: "debug"

# If using GitHub, specify like the following:
github: {}
# github:
#   user: foo
#   token: bar
#   secret: baz
# GitHub Enterprise only:
#   hostname: github.your.org
# (The chart will perform the base64 encoding for you for values that are stored in secrets.)

# If using a GitHub App, please enter your values as follows:
# githubApp:
#   id: 123456
#   slug: foo
#   key: |
#     -----BEGIN PRIVATE KEY-----
#     ...
#     -----END PRIVATE KEY-----
#   secret: baz
# (The chart will perform the base64 encoding for you for values that are stored in secrets.)

# If using GitLab, specify like the following:
# gitlab:
#   user: foo
#   token: bar
#   secret: baz
# GitLab Enterprise only:
#   hostname: gitlab.your.org
# (The chart will perform the base64 encoding for you for values that are stored in secrets.)

# If using Bitbucket, specify like the following:
bitbucket:
  config:
    known_hosts: |
      bitbucket.org ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBPIQmuzMBuKdWeF4+a2sjSSpBK0iqitSQ+5BM9KhpexuGt20JpTVM7u5BDZngncgrqDMbWdxMWWOGtZ9UgbqgZE=
    ssh_config: |
      Host bitbucket.org
        AddKeysToAgent yes
        IdentityFile ~/.ssh/id_rsa
#   user: foo
#   token: bar
# Bitbucket Server only:
#   secret: baz
#   baseURL: https://bitbucket.yourorganization.com
# (The chart will perform the base64 encoding for you for values that are stored in secrets.)

# If using Azure DevOps, specify like the following:
# azuredevops:
#   user: foo
#   token: bar
#   webhookUser: foo
#   webhookPassword: baz
# (The chart will perform the base64 encoding for you for values that are stored in secrets.)

# If managing secrets outside the chart for the webhook, use this variable to reference the secret name
# vcsSecretName: 'mysecret'

# When referencing Terraform modules in private repositories, it may be helpful
# (necessary?) to use redirection in a .gitconfig like so:
# gitconfig: |
# [url "https://<EMAIL>"]
#   insteadOf = https://github.com
# [url "https://<EMAIL>"]
#   insteadOf = ssh://**************
# [url "https://oauth2:<EMAIL>"]
#   insteadOf = https://gitlab.com
# [url "https://oauth2:<EMAIL>"]
#   insteadOf = ssh://**************
# Source: https://stackoverflow.com/questions/42148841/github-clone-with-oauth-access-token

# If managing secrets outside the chart for the gitconfig, use this variable to reference the secret name
# gitconfigSecretName: 'mygitconfigsecret'

# When referencing Terraform modules in private repositories or registries (such as Artfactory)
# configuing a .netrc file for authentication may be required:
# netrc: |
# machine artifactory.myapp.com login YOUR_USERNAME password YOUR_PASSWORD
# machine bitbucket.myapp.com login YOUR_USERNAME password YOUR_PASSWORD

# If managing secrets outside the chart for the netrc file, use this variable to reference the secret name
# netrcSecretName: 'mynetrcsecret'

# To specify AWS credentials to be mapped to ~/.aws or to aws.directory:
aws: {}
#   credentials: |
#     [default]
#     aws_access_key_id=YOUR_ACCESS_KEY_ID
#     aws_secret_access_key=YOUR_SECRET_ACCESS_KEY
#     region=us-east-1
#   config: |
#     [profile a_role_to_assume]
#     role_arn = arn:aws:iam::*********:role/service-role/roleToAssume
#     source_profile = default
#   directory: "/home/<USER>/.aws"
# To reference an already existing Secret object with AWS credentials
# awsSecretName: 'mysecretwithawscreds'

## To be used for mounting credential files (when using google provider).
serviceAccountSecrets:
  # credentials: <json file as base64 encoded string>
  # credentials-staging: <json file as base64 encoded string>

## -------------------------- ##
# Default values for atlantis (override as needed).
## -------------------------- ##

image:
  repository: ghcr.io/runatlantis/atlantis
  tag: v0.24.4
  pullPolicy: IfNotPresent

## Optionally specify an array of imagePullSecrets.
## Secrets must be manually created in the namespace.
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
##
# imagePullSecrets:
# - myRegistryKeySecretName

## Use Server Side Repo Config,
## ref: https://www.runatlantis.io/docs/server-side-repo-config.html
## Example default configuration
# repoConfig: |
#  ---
#  repos:
#  - id: /.*/
#    apply_requirements: []
#    workflow: default
#    allowed_overrides: []
#    allow_custom_workflows: false
#  workflows:
#    default:
#      plan:
#        steps: [init, plan]
#      apply:
#        steps: [apply]

# allowForkPRs enables atlantis to run on a fork Pull Requests
allowForkPRs: false

# allowDraftPRs enables atlantis to run on a draft Pull Requests
allowDraftPRs: false

# hidePrevPlanComments enables atlantis to hide previous plan comments
hidePrevPlanComments: false

## defaultTFVersion set the default terraform version to be used in atlantis server
# defaultTFVersion: 0.12.0

# disableApply disables running `atlantis apply` regardless of which flags are sent with it
disableApply: false

# disableApplyAll disables running `atlantis apply` without any flags
disableApplyAll: false

# disableRepoLocking stops atlantis locking projects and or workspaces when running terraform
disableRepoLocking: false

# Use Diff Markdown Format for color coding diffs
enableDiffMarkdownFormat: false

# Optionally specify an username and a password for basic authentication
# basicAuth:
#   username: "atlantis"
#   password: "atlantis"

# Optionally specify an API secret to enable the API
# api:
#   secret: "s3cr3t"

# If managing secrets outside the chart for the API secret, use this variable to reference the secret name
# apiSecretName: "myapisecret"

# Common Labels for all resources created by this chart.
commonLabels: {}

# We only need to check every 60s since Atlantis is not a high-throughput service.
livenessProbe:
  enabled: true
  periodSeconds: 60
  initialDelaySeconds: 5
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 5
  scheme: HTTP
readinessProbe:
  enabled: true
  periodSeconds: 60
  initialDelaySeconds: 5
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 5
  scheme: HTTP

service:
  type: NodePort
  port: 80
  targetPort: 4141
  loadBalancerIP: null

podTemplate:
  annotations:
    {}
    # kube2iam example:
    # iam.amazonaws.com/role: role-arn
  labels: {}

# It is not recommended to run atlantis as root
statefulSet:
  annotations: {}
  labels: {}
  securityContext:
    fsGroup: 1000
    runAsUser: 100
    fsGroupChangePolicy: "OnRootMismatch"
  priorityClassName: ""
  updateStrategy: {}
  # option to share process namespace with atlantis container
  shareProcessNamespace: false

## Optionally customize the terminationGracePeriodSeconds
# terminationGracePeriodSeconds: 60

ingress:
  enabled: false
  ingressClassName: atlantis
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  path: /*
  ## this is in case we want several paths under the same host, with different backend services
  #  paths:
  #    - path: "/path1"
  #      service: test1
  #      port:
  #    - path: "/path2"
  #      service: test2
  #      port:
  pathType: ImplementationSpecific
  host:

  ## in case we need several hosts:
  hosts:
  #   - host: chart-example.local
  #     paths: ["/"]
  #     service: chart-example1
  #   - host: chart-example.local2
  #     service: chart-example1
  #     paths: ["/lala"]
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
  labels: {}

## Allow to override the /etc/ssl/certs/ca-certificates.cer with your custom one
# You have to create a secret `my-ca-certificates`
# customPem: my-ca-certificates

resources:
  requests:
    memory: 1Gi
    cpu: 100m
  limits:
    memory: 1Gi
    cpu: 100m

## Embedded data volume & volumeMount (default working)
volumeClaim:
  enabled: true
  ## Disk space for to check out repositories
  dataStorage: 5Gi
  ## Storage class name (if possible, use a resizable one)
  # storageClassName: value

## To keep backwards compatibility
## DEPRECATED - Disk space for Atlantis to check out repositories
# dataStorage: 5Gi
## DEPRECATED - Storage class name for Atlantis disk
# storageClassName: value

replicaCount: 1

## test container details
test:
  enabled: true
  image: lachlanevenson/k8s-kubectl
  imageTag: v1.4.8-bash

nodeSelector: {}

tolerations: []

affinity: {}

serviceAccount:
  # Specifies whether a ServiceAccount should be created
  create: false
  # Set the `automountServiceAccountToken` field on the pod template spec
  # If false, no kubernetes service account token will be mounted to the pod
  mount: true
  # The name of the ServiceAccount to use.
  # If not set and create is true, a name is generated using the fullname template
  name: atlantis
  # Annotations for the Service Account
  # Example:
  #
  # annotations:
  #   annotation1: value
  #   annotation2: value
  annotations: {}

# Optionally deploy rbac to allow for the serviceAccount to manage terraform state via the kubernetes backend
enableKubernetesBackend: false

# tlsSecretName: tls

environment: {}
# environment:
#   ATLANTIS_DEFAULT_TF_VERSION: v1.2.9

# Optionally specify additional environment variables to be populated from Kubernetes secrets.
# Useful for passing in TF_VAR_foo or other secret environment variables from Kubernetes secrets.
environmentSecrets: []
# environmentSecrets:
#   - name: THE_ENV_VAR
#     secretKeyRef:
#       name: the_k8s_secret_name
#       key: the_key_of_the_value_in_the_secret

# Optionally specify additional environment variables in raw yaml format
# Useful to specify variables refering to k8s objects
# environmentRaw:
#   - name: POD_IP
#     valueFrom:
#       fieldRef:
#         fieldPath: status.podIP
environmentRaw: []

# Optionally specify additional Kubernetes secrets to load environment variables from.
# All key-value pairs within these secrets will be set as environment variables.
# Note that any variables set here will be ignored if also defined in the env block of the atlantis statefulset.
# For example, providing ATLANTIS_GH_USER here and defining a value for github.user will result in the github.user value being used.
loadEnvFromSecrets: []
# loadEnvFromSecrets:
#   - secret_one
#   - secret_two

# Optionally specify additional Kubernetes ConfigMaps to load environment variables from.
# All key-value pairs within these ConfigMaps will be set as environment variables.
# Note that any variables set here will be ignored if also defined in the env block of the atlantis statefulset.
# For example, providing ATLANTIS_ALLOW_FORK_PRS here and defining a value for allowForkPRs will result in the allowForkPRs value being used.
loadEnvFromConfigMaps: []
# loadEnvFromConfigMaps:
#   - config_one
#   - config_two

# Optionally specify google service account credentials as Kubernetes secrets. If you are using the terraform google provider you can specify the credentials as "${file("/var/secrets/some-secret-name/key.json")}".
googleServiceAccountSecrets: []
# googleServiceAccountSecrets:
#   - name: some-secret-name
#     secretName: the_k8s_secret_name

# Optionally specify additional volumes for the pod.
extraVolumes: []
# extraVolumes:
#   - name: some-volume-name
#     emptyDir: {}

# Optionally specify additional volume mounts for the container.
extraVolumeMounts: []
# extraVolumeMounts:
#   - name: some-volume-name
#     mountPath: /path/in/container

extraManifests: []
# extraManifests:
#  - apiVersion: cloud.google.com/v1beta1
#    kind: BackendConfig
#    metadata:
#      name: "{{ .Release.Name }}-test"
#    spec:
#      securityPolicy:
#        name: "gcp-cloud-armor-policy-test"

initContainers: []
# initContainers:
# - name: example
#   image: alpine:latest
#   command: ['sh', '-c']

# hostAliases:
#   - hostnames:
#     - aaa.com
#     - test.ccc.com
#     ip: 10.0.0.0
#   - hostnames:
#     - bbb.com
#     ip: ********

extraArgs: []
# extraArgs:
# - --disable-autoplan
# - --disable-repo-locking

extraContainers: []
# extraContainers:
#  - name: <container name>
#    args:
#      - ...
#    image: <docker images>
#    imagePullPolicy: IfNotPresent
#    resources:
#      limits:
#        memory: 128Mi
#      requests:
#        cpu: 100m
#        memory: 128Mi
#    volumeMounts:
#      - ...

containerSecurityContext: {}
# containerSecurityContext:
#   allowPrivilegeEscalation: false
#   readOnlyRootFilesystem: true

servicemonitor:
  enabled: false

# Enable this if you're using Google Managed Prometheus
podMonitor:
  enabled: false
  interval: "30s"

# Set the desired Locking DB type
# lockingDbType: <boltdb|redis>

# Configure Redis Locking DB
# lockingDbType value must be redis for the config to take effect
redis: {}
#  host: redis.host.name
#  password: myRedisPassword
#  port: 6379
#  db: 0
#  tlsEnabled: false
#  insecureSkipVerify: false

# If managing secrets outside the chart for the Redis secret, use this variable to reference the secret name
# redisSecretName: "myRedisSecret"

# Set lifecycle hooks https://kubernetes.io/docs/tasks/configure-pod-container/attach-handler-lifecycle-event/
lifecycle: {}

config: |
  ---
  webhooks:
    - event: apply
      workspace-regex: .*
      branch-regex: .*
      kind: slack
      channel: terraform