name: Unit Test
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
    - name: Set up Go 1.x
      uses: actions/setup-go@v2
      with:
        go-version: ^1.15
 
    - name: Check out code into the Go module directory
      uses: actions/checkout@v2

    - name: Run unit tests
      run: |
        sudo snap install yq
        make test
        
    - name: Codecov
      uses: codecov/codecov-action@v1
      with:
        file: ./cover.out # optional
