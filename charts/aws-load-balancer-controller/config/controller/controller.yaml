apiVersion: v1
kind: ServiceAccount
metadata:
  name: controller
  labels:
    app.kubernetes.io/component: controller
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller
  labels:
    app.kubernetes.io/component: controller
spec:
  selector:
    matchLabels:
      app.kubernetes.io/component: controller
  replicas: 1
  template:
    metadata:
      labels:
        app.kubernetes.io/component: controller
    spec:
      containers:
        - name: controller
          image: controller:latest
          args:
            - --cluster-name=your-cluster-name
            - --ingress-class=alb
          resources:
            limits:
              cpu: 200m
              memory: 500Mi
            requests:
              cpu: 100m
              memory: 200Mi
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /healthz
              port: 61779
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 10
      terminationGracePeriodSeconds: 10
      priorityClassName: system-cluster-critical
      serviceAccountName: controller