# Requirements:
#
#    - For this template, Cognito should have the following basic settings:
#        - User Pool ARN ( Cognito -> General Settings )
#            `arn:aws:cognito-idp:<region>:<account-id>:userpool/<region><cognito-id>`
#        - User Pool Client ID ( Cognito -> App Integration -> Application Client Settings )
#            `<user-pool-client-id>`
#        - Domain Name ( Cognito -> App Integration -> Domain Name)
#            `<user-pool-authentication-domain>`
#        - OAuth Scopes ( Cognito -> App Integration -> Application Client Settings )
#            `[x] openid`
#        - OAuth Flows ( Cognito -> App Integration -> Application Client Settings )
#            `[x] Authorization code grant`
#        - Callback URL(s) ( Cognito -> App Integration -> Application Client Settings )
#            `https://<app-name>.<your-domain>/oauth2/idpresponse`
#
#    - Related Kubernetes service/application
#

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: <ingress-name>  # app.example.com
  namespace: <placement-namespace>  # default
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/tags: Environment=<environment-name>,Owner=<your-name>
    # For each `listen-ports` object defined an ALB lister is created
    # For each listener created the rules defined in `spec` apply with some basic caveats
    # SSL redirect rule is applied only to the HTTP listener.  Cognito authentication rule
    # is applied to the HTTPS listener
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    # Detailed redirect settings
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    # Authentication type must be cognito
    alb.ingress.kubernetes.io/auth-type: cognito
    # Required parameter for ALB/Cognito integration
    alb.ingress.kubernetes.io/auth-scope: openid
    # Session timeout on authentication credentials
    alb.ingress.kubernetes.io/auth-session-timeout: '3600'
    # Session cookie name
    alb.ingress.kubernetes.io/auth-session-cookie: AWSELBAuthSessionCookie
    # Action to take when a request is not authenticated
    alb.ingress.kubernetes.io/auth-on-unauthenticated-request: authenticate
    # Cognito parameters required for creation of authentication rules
    # The subdomain name only is sufficient for `UserPoolDomain`
    # e.g. if `FQDN=app.auth.ap-northeast-1.amazoncognito.com` then `UserPoolDomain=app`
    alb.ingress.kubernetes.io/auth-idp-cognito: '{"UserPoolArn": "arn:aws:cognito-idp:<region>:<account-id>:userpool/<region><cognito-id>","UserPoolClientId":"<user-pool-client-id>","UserPoolDomain":"<user-pool-authentication-domain>"}'
    # ACM certificate ARN for your SSL domain
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:<region>:<account-id>:certificate/<certificate-id>
spec:
  ingressClassName: alb
  rules:
    # If you are using ExternalDNS, this will become your applications FQDN
    - host: <FQDN>
      http:
        paths:
          # This first path should perform an ssl-redirect as below
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ssl-redirect
                # Configured via the redirect settings in the annotations
                port:
                  name: use-annotation
          - path: <html-path>
            pathType: Prefix
            backend:
              service:
                name: <service-name>
                port:
                  number: <service-port>
