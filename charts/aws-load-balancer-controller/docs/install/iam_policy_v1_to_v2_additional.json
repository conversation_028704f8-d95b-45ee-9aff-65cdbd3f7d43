{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:CreateTags", "ec2:DeleteTags"], "Resource": "arn:aws:ec2:*:*:security-group/*", "Condition": {"Null": {"aws:ResourceTag/ingress.k8s.aws/cluster": "false"}}}, {"Effect": "Allow", "Action": ["elasticloadbalancing:AddTags", "elasticloadbalancing:RemoveTags", "elasticloadbalancing:DeleteTargetGroup"], "Resource": ["arn:aws:elasticloadbalancing:*:*:targetgroup/*/*", "arn:aws:elasticloadbalancing:*:*:loadbalancer/net/*/*", "arn:aws:elasticloadbalancing:*:*:loadbalancer/app/*/*"], "Condition": {"Null": {"aws:ResourceTag/ingress.k8s.aws/cluster": "false"}}}]}