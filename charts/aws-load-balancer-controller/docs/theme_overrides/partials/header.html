<header class="md-header" data-md-component="header">
    <nav
      class="md-header__inner md-grid"
      aria-label="{{ lang.t('header.title') }}"
    >
  
      <!-- Link to home -->
      <a
        href="{{ config.extra.homepage | d(nav.homepage.url, true) | url }}"
        title="{{ config.site_name | e }}"
        class="md-header__button md-logo"
        aria-label="{{ config.site_name }}"
        data-md-component="logo"
      >
        {% include "partials/logo.html" %}
      </a>
  
      <!-- Button to open drawer -->
      <label class="md-header__button md-icon" for="__drawer">
        {% include ".icons/material/menu" ~ ".svg" %}
      </label>
  
      <!-- Header title -->
      <div class="md-header__title" data-md-component="header-title">
        <div class="md-header__ellipsis">
          <div class="md-header__topic">
            <span class="md-ellipsis">
              {{ config.site_name }}
            </span>
          </div>
          <div class="md-header__topic" data-md-component="header-topic">
            <span class="md-ellipsis">
              {% if page and page.meta and page.meta.title %}
                {{ page.meta.title }}
              {% else %}
                {{ page.title }}
              {% endif %}
            </span>
          </div>
        </div>
      </div>
  
      <!-- Color palette -->
      {% if not config.theme.palette is mapping %}
        <form class="md-header__option" data-md-component="palette">
          {% for option in config.theme.palette %}
            {% set primary = option.primary | replace(" ", "-") | lower %}
            {% set accent  = option.accent  | replace(" ", "-") | lower %}
            <input
              class="md-option"
              data-md-color-media="{{ option.media }}"
              data-md-color-scheme="{{ option.scheme }}"
              data-md-color-primary="{{ primary }}"
              data-md-color-accent="{{ accent }}"
              type="radio"
              name="__palette"
              id="__palette_{{ loop.index }}"
            />
            {% if option.toggle %}
              <label
                class="md-header__button md-icon"
                title="{{ option.toggle.name }}"
                for="__palette_{{ loop.index0 or loop.length }}"
                hidden
              >
                {% include ".icons/" ~ option.toggle.icon ~ ".svg" %}
              </label>
            {% endif %}
          {% endfor %}
        </form>
      {% endif %}

      {% include "partials/version-select.html" %}
      <!-- Site language selector -->
      {% if config.extra.alternate %}
        <div class="md-header__option"></form>
          <div class="md-select">
            {% set icon = config.theme.icon.alternate or "material/translate" %}
            <button class="md-header__button md-icon">
              {% include ".icons/" ~ icon ~ ".svg" %}
            </button>
            <div class="md-select__inner">
              <ul class="md-select__list">
                {% for alt in config.extra.alternate %}
                  <li class="md-select__item">
                    <a
                      href="{{ alt.link | url }}"
                      hreflang="{{ alt.lang }}"
                      class="md-select__link"
                    >
                      {{ alt.name }}
                    </a>
                  </li>
                  {% endfor %}
              </ul>
            </div>
          </div>
        </div>
      {% endif %}
  
      <!-- Button to open search modal -->
      {% if "search" in config["plugins"] %}
        <label class="md-header__button md-icon" for="__search">
          {% include ".icons/material/magnify.svg" %}
        </label>
  
        <!-- Search interface -->
        {% include "partials/search.html" %}
      {% endif %}
  
      <!-- Repository information -->
      {% if config.repo_url %}
        <div class="md-header__source">
          {% include "partials/source.html" %}
        </div>
      {% endif %}
    </nav>
  </header>