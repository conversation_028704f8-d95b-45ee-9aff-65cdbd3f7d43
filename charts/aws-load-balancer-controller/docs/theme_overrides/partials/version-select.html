<div id="version-select" class="mdc-select" data-base-url="{{ base_url }}">
    <div class="mdc-select__anchor" role="button">
        <span class="mdc-select__ripple"></span>
        <span class="mdc-select__selected-text-container">
            <span class="mdc-select__selected-text"></span>
        </span>
        <span class="mdc-select__dropdown-icon">
            <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd"
                    points="7 10 12 15 17 10">
                </polygon>
                <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd"
                    points="7 15 12 10 17 15">
                </polygon>
            </svg>
        </span>
    </div>

    <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
        <ul class="mdc-list" role="listbox">
        </ul>
    </div>
</div>