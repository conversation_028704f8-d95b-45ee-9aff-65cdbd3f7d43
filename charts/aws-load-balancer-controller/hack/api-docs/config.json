{"hideMemberFields": ["TypeMeta"], "hideTypePatterns": ["ParseError$", "List$"], "externalPackages": [{"typeMatchPrefix": "^k8s\\.io/apimachinery/pkg/apis/meta/v1\\.Duration$", "docsURLTemplate": "https://godoc.org/k8s.io/apimachinery/pkg/apis/meta/v1#Duration"}, {"typeMatchPrefix": "^k8s\\.io/(api|apimachinery/pkg/apis)/", "docsURLTemplate": "https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.16/#{{lower .TypeIdentifier}}-{{arrIndex .PackageSegments -1}}-{{arrIndex .PackageSegments -2}}"}, {"typeMatchPrefix": "^github\\.com/knative/pkg/apis/duck/", "docsURLTemplate": "https://godoc.org/github.com/knative/pkg/apis/duck/{{arrIndex .PackageSegments -1}}#{{.TypeIdentifier}}"}], "typeDisplayNamePrefixOverrides": {"k8s.io/api/": "Kubernetes ", "k8s.io/apimachinery/pkg/apis/": "Kubernetes "}, "markdownDisabled": false}