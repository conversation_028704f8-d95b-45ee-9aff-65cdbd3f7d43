{{- if and .Values.podDisruptionBudget (gt (int .Values.replicaCount) 1) }}
{{- if .Capabilities.APIVersions.Has "policy/v1/PodDisruptionBudget" }}
apiVersion: policy/v1
{{- else }}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "aws-load-balancer-controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "aws-load-balancer-controller.selectorLabels" . | nindent 6 }}
  {{- toYaml .Values.podDisruptionBudget | nindent 2 }}
{{- end }}
