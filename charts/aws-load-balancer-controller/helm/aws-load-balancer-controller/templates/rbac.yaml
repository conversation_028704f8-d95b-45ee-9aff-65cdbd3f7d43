{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "aws-load-balancer-controller.fullname" . }}-leader-election-role
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
rules:
- apiGroups: [""]
  resources: [configmaps]
  verbs: [create]
- apiGroups: [""]
  resources: [configmaps]
  resourceNames: [aws-load-balancer-controller-leader]
  verbs: [get, patch, update]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "aws-load-balancer-controller.fullname" . }}-leader-election-rolebinding
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "aws-load-balancer-controller.fullname" . }}-leader-election-role
subjects:
- kind: ServiceAccount
  name: {{ template "aws-load-balancer-controller.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "aws-load-balancer-controller.fullname" . }}-role
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
rules:
- apiGroups: ["elbv2.k8s.aws"]
  resources: [targetgroupbindings]
  verbs: [create, delete, get, list, patch, update, watch]
- apiGroups: ["elbv2.k8s.aws"]
  resources: [ingressclassparams]
  verbs: [get, list, watch]
- apiGroups: [""]
  resources: [events]
  verbs: [create, patch]
- apiGroups: [""]
  resources: [pods]
  verbs: [get, list, watch]
- apiGroups: ["networking.k8s.io"]
  resources: [ingressclasses]
  verbs: [get, list, watch]
- apiGroups: ["", "extensions", "networking.k8s.io"]
  resources: [services, ingresses]
  verbs: [get, list, patch, update, watch]
- apiGroups: [""]
  resources: [nodes, namespaces, endpoints]
  verbs: [get, list, watch]
{{- if .Values.clusterSecretsPermissions.allowAllSecrets }}
- apiGroups: [""]
  resources: [secrets]
  verbs: [get, list, watch]
{{- end }}
- apiGroups: ["elbv2.k8s.aws", "", "extensions", "networking.k8s.io"]
  resources: [targetgroupbindings/status, pods/status, services/status, ingresses/status]
  verbs: [update, patch]
- apiGroups: ["discovery.k8s.io"]
  resources: [endpointslices]
  verbs: [get, list, watch]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "aws-load-balancer-controller.fullname" . }}-rolebinding
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "aws-load-balancer-controller.fullname" . }}-role
subjects:
- kind: ServiceAccount
  name: {{ template "aws-load-balancer-controller.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
