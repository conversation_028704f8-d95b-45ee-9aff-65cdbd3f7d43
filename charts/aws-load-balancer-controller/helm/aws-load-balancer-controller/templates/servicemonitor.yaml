{{- if.Values.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "aws-load-balancer-controller.fullname" . }}
  {{- if .Values.serviceMonitor.namespace }}
  namespace: {{ .Values.serviceMonitor.namespace }}
  {{- else }}
  namespace: {{ .Release.Namespace | quote }}
  {{- end }}
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
    {{- with .Values.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  jobLabel: {{ .Release.Name }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  selector:
    matchLabels:
      {{- include "aws-load-balancer-controller.selectorLabels" . | nindent 6 }}
    matchExpressions:
      - key: prometheus.io/service-monitor
        operator: NotIn
        values:
          - "false"
  endpoints:
    - port: metrics-server
      path: /metrics
      {{- with .Values.serviceMonitor.interval }}
      interval: {{ . }}
      {{- end }}
{{- end -}}