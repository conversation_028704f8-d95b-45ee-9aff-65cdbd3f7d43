{{ $tls := fromYaml ( include "aws-load-balancer-controller.webhookCerts" . ) }}
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
{{- if $.Values.enableCertManager }}
  annotations:
    cert-manager.io/inject-ca-from: {{ .Release.Namespace }}/{{ template "aws-load-balancer-controller.namePrefix" . }}-serving-cert
{{- end }}
  name: {{ include "aws-load-balancer-controller.namePrefix" . }}-webhook
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
webhooks:
- clientConfig:
    caBundle: {{ if not $.Values.enableCertManager -}}{{ $tls.caCert }}{{- else -}}Cg=={{ end }}
    service:
      name: {{ template "aws-load-balancer-controller.webhookService" . }}
      namespace: {{ $.Release.Namespace }}
      path: /mutate-v1-pod
  failurePolicy: Fail
  name: mpod.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  namespaceSelector:
    matchExpressions:
    - key: elbv2.k8s.aws/pod-readiness-gate-inject
      operator: In
      values:
      - enabled
  objectSelector:
    matchExpressions:
    - key: app.kubernetes.io/name
      operator: NotIn
      values:
      - {{ include "aws-load-balancer-controller.name" . }}
    {{- if .Values.objectSelector.matchExpressions }}
    {{- toYaml .Values.objectSelector.matchExpressions | nindent 4 }}
    {{- end }}
    {{- if .Values.objectSelector.matchLabels }}
    matchLabels:
    {{- toYaml .Values.objectSelector.matchLabels | nindent 6 }}
    {{- end }}
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
  sideEffects: None
- clientConfig:
    caBundle: {{ if not $.Values.enableCertManager -}}{{ $tls.caCert }}{{- else -}}Cg=={{ end }}
    service:
      name: {{ template "aws-load-balancer-controller.webhookService" . }}
      namespace: {{ $.Release.Namespace }}
      path: /mutate-elbv2-k8s-aws-v1beta1-targetgroupbinding
  failurePolicy: Fail
  name: mtargetgroupbinding.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  rules:
  - apiGroups:
    - elbv2.k8s.aws
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - targetgroupbindings
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
{{- if $.Values.enableCertManager }}
  annotations:
    cert-manager.io/inject-ca-from: {{ .Release.Namespace }}/{{ template "aws-load-balancer-controller.namePrefix" . }}-serving-cert
{{- end }}
  name: {{ include "aws-load-balancer-controller.namePrefix" . }}-webhook
  labels:
    {{- include "aws-load-balancer-controller.labels" . | nindent 4 }}
webhooks:
- clientConfig:
    caBundle: {{ if not $.Values.enableCertManager -}}{{ $tls.caCert }}{{- else -}}Cg=={{ end }}
    service:
      name: {{ template "aws-load-balancer-controller.webhookService" . }}
      namespace: {{ $.Release.Namespace }}
      path: /validate-elbv2-k8s-aws-v1beta1-targetgroupbinding
  failurePolicy: Fail
  name: vtargetgroupbinding.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  rules:
  - apiGroups:
    - elbv2.k8s.aws
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - targetgroupbindings
  sideEffects: None
- clientConfig:
    caBundle: {{ if not $.Values.enableCertManager -}}{{ $tls.caCert }}{{- else -}}Cg=={{ end }}
    service:
      name: {{ template "aws-load-balancer-controller.webhookService" . }}
      namespace: {{ $.Release.Namespace }}
      path: /validate-networking-v1-ingress
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: vingress.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  rules:
  - apiGroups:
    - networking.k8s.io
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - ingresses
  sideEffects: None
---
{{- if not $.Values.enableCertManager }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "aws-load-balancer-controller.webhookCertSecret" . }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "aws-load-balancer-controller.labels" . | indent 4 }}
type: kubernetes.io/tls
data:
  ca.crt: {{ $tls.caCert }}
  tls.crt: {{ $tls.clientCert }}
  tls.key: {{ $tls.clientKey }}
{{- else }}
{{- if .Capabilities.APIVersions.Has "cert-manager.io/v1" }}
apiVersion: cert-manager.io/v1
{{- else }}
apiVersion: cert-manager.io/v1alpha2
{{- end }}
kind: Certificate
metadata:
  name: {{ template "aws-load-balancer-controller.namePrefix" . }}-serving-cert
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "aws-load-balancer-controller.labels" . | indent 4 }}
spec:
  dnsNames:
  - {{ template "aws-load-balancer-controller.webhookService" . }}.{{ .Release.Namespace }}.svc
  - {{ template "aws-load-balancer-controller.webhookService" . }}.{{ .Release.Namespace }}.svc.cluster.local
  issuerRef:
    kind: Issuer
    name: {{ template "aws-load-balancer-controller.namePrefix" . }}-selfsigned-issuer
  secretName: {{ template "aws-load-balancer-controller.webhookCertSecret" . }}
---
{{- if .Capabilities.APIVersions.Has "cert-manager.io/v1" }}
apiVersion: cert-manager.io/v1
{{- else }}
apiVersion: cert-manager.io/v1alpha2
{{- end }}
kind: Issuer
metadata:
  name: {{ template "aws-load-balancer-controller.namePrefix" . }}-selfsigned-issuer
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "aws-load-balancer-controller.labels" . | indent 4 }}
spec:
  selfSigned: {}
{{- end }}
