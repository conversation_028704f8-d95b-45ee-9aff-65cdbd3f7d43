package annotations

const (
	// IngressClass
	IngressClass = "kubernetes.io/ingress.class"

	AnnotationPrefixIngress = "alb.ingress.kubernetes.io"
	// Ingress annotation suffixes
	IngressSuffixLoadBalancerName             = "load-balancer-name"
	IngressSuffixGroupName                    = "group.name"
	IngressSuffixGroupOrder                   = "group.order"
	IngressSuffixTags                         = "tags"
	IngressSuffixIPAddressType                = "ip-address-type"
	IngressSuffixScheme                       = "scheme"
	IngressSuffixSubnets                      = "subnets"
	IngressSuffixCustomerOwnedIPv4Pool        = "customer-owned-ipv4-pool"
	IngressSuffixLoadBalancerAttributes       = "load-balancer-attributes"
	IngressSuffixWAFv2ACLARN                  = "wafv2-acl-arn"
	IngressSuffixWAFACLID                     = "waf-acl-id"
	IngressSuffixWebACLID                     = "web-acl-id" // deprecated, use "waf-acl-id" instead.
	IngressSuffixShieldAdvancedProtection     = "shield-advanced-protection"
	IngressSuffixSecurityGroups               = "security-groups"
	IngressSuffixListenPorts                  = "listen-ports"
	IngressSuffixSSLRedirect                  = "ssl-redirect"
	IngressSuffixInboundCIDRs                 = "inbound-cidrs"
	IngressSuffixCertificateARN               = "certificate-arn"
	IngressSuffixSSLPolicy                    = "ssl-policy"
	IngressSuffixTargetType                   = "target-type"
	IngressSuffixBackendProtocol              = "backend-protocol"
	IngressSuffixBackendProtocolVersion       = "backend-protocol-version"
	IngressSuffixTargetGroupAttributes        = "target-group-attributes"
	IngressSuffixHealthCheckPort              = "healthcheck-port"
	IngressSuffixHealthCheckProtocol          = "healthcheck-protocol"
	IngressSuffixHealthCheckPath              = "healthcheck-path"
	IngressSuffixHealthCheckIntervalSeconds   = "healthcheck-interval-seconds"
	IngressSuffixHealthCheckTimeoutSeconds    = "healthcheck-timeout-seconds"
	IngressSuffixHealthyThresholdCount        = "healthy-threshold-count"
	IngressSuffixUnhealthyThresholdCount      = "unhealthy-threshold-count"
	IngressSuffixSuccessCodes                 = "success-codes"
	IngressSuffixAuthType                     = "auth-type"
	IngressSuffixAuthIDPCognito               = "auth-idp-cognito"
	IngressSuffixAuthIDPOIDC                  = "auth-idp-oidc"
	IngressSuffixAuthOnUnauthenticatedRequest = "auth-on-unauthenticated-request"
	IngressSuffixAuthScope                    = "auth-scope"
	IngressSuffixAuthSessionCookie            = "auth-session-cookie"
	IngressSuffixAuthSessionTimeout           = "auth-session-timeout"
	IngressSuffixTargetNodeLabels             = "target-node-labels"
	IngressSuffixManageSecurityGroupRules     = "manage-backend-security-group-rules"

	// NLB annotation suffixes
	// prefixes service.beta.kubernetes.io, service.kubernetes.io
	SvcLBSuffixSourceRanges                  = "load-balancer-source-ranges"
	SvcLBSuffixLoadBalancerType              = "aws-load-balancer-type"
	SvcLBSuffixTargetType                    = "aws-load-balancer-nlb-target-type"
	SvcLBSuffixLoadBalancerName              = "aws-load-balancer-name"
	SvcLBSuffixScheme                        = "aws-load-balancer-scheme"
	SvcLBSuffixInternal                      = "aws-load-balancer-internal"
	SvcLBSuffixProxyProtocol                 = "aws-load-balancer-proxy-protocol"
	SvcLBSuffixIPAddressType                 = "aws-load-balancer-ip-address-type"
	SvcLBSuffixAccessLogEnabled              = "aws-load-balancer-access-log-enabled"
	SvcLBSuffixAccessLogS3BucketName         = "aws-load-balancer-access-log-s3-bucket-name"
	SvcLBSuffixAccessLogS3BucketPrefix       = "aws-load-balancer-access-log-s3-bucket-prefix"
	SvcLBSuffixCrossZoneLoadBalancingEnabled = "aws-load-balancer-cross-zone-load-balancing-enabled"
	SvcLBSuffixSSLCertificate                = "aws-load-balancer-ssl-cert"
	SvcLBSuffixSSLPorts                      = "aws-load-balancer-ssl-ports"
	SvcLBSuffixSSLNegotiationPolicy          = "aws-load-balancer-ssl-negotiation-policy"
	SvcLBSuffixBEProtocol                    = "aws-load-balancer-backend-protocol"
	SvcLBSuffixAdditionalTags                = "aws-load-balancer-additional-resource-tags"
	SvcLBSuffixHCHealthyThreshold            = "aws-load-balancer-healthcheck-healthy-threshold"
	SvcLBSuffixHCUnhealthyThreshold          = "aws-load-balancer-healthcheck-unhealthy-threshold"
	SvcLBSuffixHCTimeout                     = "aws-load-balancer-healthcheck-timeout"
	SvcLBSuffixHCInterval                    = "aws-load-balancer-healthcheck-interval"
	SvcLBSuffixHCProtocol                    = "aws-load-balancer-healthcheck-protocol"
	SvcLBSuffixHCPort                        = "aws-load-balancer-healthcheck-port"
	SvcLBSuffixHCPath                        = "aws-load-balancer-healthcheck-path"
	SvcLBSuffixEIPAllocations                = "aws-load-balancer-eip-allocations"
	SvcLBSuffixPrivateIpv4Addresses          = "aws-load-balancer-private-ipv4-addresses"
	SvcLBSuffixTargetGroupAttributes         = "aws-load-balancer-target-group-attributes"
	SvcLBSuffixSubnets                       = "aws-load-balancer-subnets"
	SvcLBSuffixALPNPolicy                    = "aws-load-balancer-alpn-policy"
	SvcLBSuffixTargetNodeLabels              = "aws-load-balancer-target-node-labels"
	SvcLBSuffixLoadBalancerAttributes        = "aws-load-balancer-attributes"
	SvcLBSuffixManageSGRules                 = "aws-load-balancer-manage-backend-security-group-rules"
)
