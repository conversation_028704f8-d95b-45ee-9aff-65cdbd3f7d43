// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/aws/services (interfaces: ELBV2)

// Package services is a generated GoMock package.
package services

import (
	context "context"
	reflect "reflect"

	request "github.com/aws/aws-sdk-go/aws/request"
	elbv2 "github.com/aws/aws-sdk-go/service/elbv2"
	gomock "github.com/golang/mock/gomock"
)

// MockELBV2 is a mock of ELBV2 interface.
type MockELBV2 struct {
	ctrl     *gomock.Controller
	recorder *MockELBV2MockRecorder
}

// MockELBV2MockRecorder is the mock recorder for MockELBV2.
type MockELBV2MockRecorder struct {
	mock *MockELBV2
}

// NewMockELBV2 creates a new mock instance.
func NewMockELBV2(ctrl *gomock.Controller) *MockELBV2 {
	mock := &MockELBV2{ctrl: ctrl}
	mock.recorder = &MockELBV2MockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockELBV2) EXPECT() *MockELBV2MockRecorder {
	return m.recorder
}

// AddListenerCertificates mocks base method.
func (m *MockELBV2) AddListenerCertificates(arg0 *elbv2.AddListenerCertificatesInput) (*elbv2.AddListenerCertificatesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddListenerCertificates", arg0)
	ret0, _ := ret[0].(*elbv2.AddListenerCertificatesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddListenerCertificates indicates an expected call of AddListenerCertificates.
func (mr *MockELBV2MockRecorder) AddListenerCertificates(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddListenerCertificates", reflect.TypeOf((*MockELBV2)(nil).AddListenerCertificates), arg0)
}

// AddListenerCertificatesRequest mocks base method.
func (m *MockELBV2) AddListenerCertificatesRequest(arg0 *elbv2.AddListenerCertificatesInput) (*request.Request, *elbv2.AddListenerCertificatesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddListenerCertificatesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.AddListenerCertificatesOutput)
	return ret0, ret1
}

// AddListenerCertificatesRequest indicates an expected call of AddListenerCertificatesRequest.
func (mr *MockELBV2MockRecorder) AddListenerCertificatesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddListenerCertificatesRequest", reflect.TypeOf((*MockELBV2)(nil).AddListenerCertificatesRequest), arg0)
}

// AddListenerCertificatesWithContext mocks base method.
func (m *MockELBV2) AddListenerCertificatesWithContext(arg0 context.Context, arg1 *elbv2.AddListenerCertificatesInput, arg2 ...request.Option) (*elbv2.AddListenerCertificatesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddListenerCertificatesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.AddListenerCertificatesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddListenerCertificatesWithContext indicates an expected call of AddListenerCertificatesWithContext.
func (mr *MockELBV2MockRecorder) AddListenerCertificatesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddListenerCertificatesWithContext", reflect.TypeOf((*MockELBV2)(nil).AddListenerCertificatesWithContext), varargs...)
}

// AddTags mocks base method.
func (m *MockELBV2) AddTags(arg0 *elbv2.AddTagsInput) (*elbv2.AddTagsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTags", arg0)
	ret0, _ := ret[0].(*elbv2.AddTagsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTags indicates an expected call of AddTags.
func (mr *MockELBV2MockRecorder) AddTags(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTags", reflect.TypeOf((*MockELBV2)(nil).AddTags), arg0)
}

// AddTagsRequest mocks base method.
func (m *MockELBV2) AddTagsRequest(arg0 *elbv2.AddTagsInput) (*request.Request, *elbv2.AddTagsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTagsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.AddTagsOutput)
	return ret0, ret1
}

// AddTagsRequest indicates an expected call of AddTagsRequest.
func (mr *MockELBV2MockRecorder) AddTagsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTagsRequest", reflect.TypeOf((*MockELBV2)(nil).AddTagsRequest), arg0)
}

// AddTagsWithContext mocks base method.
func (m *MockELBV2) AddTagsWithContext(arg0 context.Context, arg1 *elbv2.AddTagsInput, arg2 ...request.Option) (*elbv2.AddTagsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddTagsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.AddTagsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTagsWithContext indicates an expected call of AddTagsWithContext.
func (mr *MockELBV2MockRecorder) AddTagsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTagsWithContext", reflect.TypeOf((*MockELBV2)(nil).AddTagsWithContext), varargs...)
}

// CreateListener mocks base method.
func (m *MockELBV2) CreateListener(arg0 *elbv2.CreateListenerInput) (*elbv2.CreateListenerOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateListener", arg0)
	ret0, _ := ret[0].(*elbv2.CreateListenerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateListener indicates an expected call of CreateListener.
func (mr *MockELBV2MockRecorder) CreateListener(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateListener", reflect.TypeOf((*MockELBV2)(nil).CreateListener), arg0)
}

// CreateListenerRequest mocks base method.
func (m *MockELBV2) CreateListenerRequest(arg0 *elbv2.CreateListenerInput) (*request.Request, *elbv2.CreateListenerOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateListenerRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.CreateListenerOutput)
	return ret0, ret1
}

// CreateListenerRequest indicates an expected call of CreateListenerRequest.
func (mr *MockELBV2MockRecorder) CreateListenerRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateListenerRequest", reflect.TypeOf((*MockELBV2)(nil).CreateListenerRequest), arg0)
}

// CreateListenerWithContext mocks base method.
func (m *MockELBV2) CreateListenerWithContext(arg0 context.Context, arg1 *elbv2.CreateListenerInput, arg2 ...request.Option) (*elbv2.CreateListenerOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateListenerWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.CreateListenerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateListenerWithContext indicates an expected call of CreateListenerWithContext.
func (mr *MockELBV2MockRecorder) CreateListenerWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateListenerWithContext", reflect.TypeOf((*MockELBV2)(nil).CreateListenerWithContext), varargs...)
}

// CreateLoadBalancer mocks base method.
func (m *MockELBV2) CreateLoadBalancer(arg0 *elbv2.CreateLoadBalancerInput) (*elbv2.CreateLoadBalancerOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLoadBalancer", arg0)
	ret0, _ := ret[0].(*elbv2.CreateLoadBalancerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLoadBalancer indicates an expected call of CreateLoadBalancer.
func (mr *MockELBV2MockRecorder) CreateLoadBalancer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLoadBalancer", reflect.TypeOf((*MockELBV2)(nil).CreateLoadBalancer), arg0)
}

// CreateLoadBalancerRequest mocks base method.
func (m *MockELBV2) CreateLoadBalancerRequest(arg0 *elbv2.CreateLoadBalancerInput) (*request.Request, *elbv2.CreateLoadBalancerOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLoadBalancerRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.CreateLoadBalancerOutput)
	return ret0, ret1
}

// CreateLoadBalancerRequest indicates an expected call of CreateLoadBalancerRequest.
func (mr *MockELBV2MockRecorder) CreateLoadBalancerRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLoadBalancerRequest", reflect.TypeOf((*MockELBV2)(nil).CreateLoadBalancerRequest), arg0)
}

// CreateLoadBalancerWithContext mocks base method.
func (m *MockELBV2) CreateLoadBalancerWithContext(arg0 context.Context, arg1 *elbv2.CreateLoadBalancerInput, arg2 ...request.Option) (*elbv2.CreateLoadBalancerOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLoadBalancerWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.CreateLoadBalancerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLoadBalancerWithContext indicates an expected call of CreateLoadBalancerWithContext.
func (mr *MockELBV2MockRecorder) CreateLoadBalancerWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLoadBalancerWithContext", reflect.TypeOf((*MockELBV2)(nil).CreateLoadBalancerWithContext), varargs...)
}

// CreateRule mocks base method.
func (m *MockELBV2) CreateRule(arg0 *elbv2.CreateRuleInput) (*elbv2.CreateRuleOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRule", arg0)
	ret0, _ := ret[0].(*elbv2.CreateRuleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRule indicates an expected call of CreateRule.
func (mr *MockELBV2MockRecorder) CreateRule(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRule", reflect.TypeOf((*MockELBV2)(nil).CreateRule), arg0)
}

// CreateRuleRequest mocks base method.
func (m *MockELBV2) CreateRuleRequest(arg0 *elbv2.CreateRuleInput) (*request.Request, *elbv2.CreateRuleOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRuleRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.CreateRuleOutput)
	return ret0, ret1
}

// CreateRuleRequest indicates an expected call of CreateRuleRequest.
func (mr *MockELBV2MockRecorder) CreateRuleRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRuleRequest", reflect.TypeOf((*MockELBV2)(nil).CreateRuleRequest), arg0)
}

// CreateRuleWithContext mocks base method.
func (m *MockELBV2) CreateRuleWithContext(arg0 context.Context, arg1 *elbv2.CreateRuleInput, arg2 ...request.Option) (*elbv2.CreateRuleOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRuleWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.CreateRuleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRuleWithContext indicates an expected call of CreateRuleWithContext.
func (mr *MockELBV2MockRecorder) CreateRuleWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRuleWithContext", reflect.TypeOf((*MockELBV2)(nil).CreateRuleWithContext), varargs...)
}

// CreateTargetGroup mocks base method.
func (m *MockELBV2) CreateTargetGroup(arg0 *elbv2.CreateTargetGroupInput) (*elbv2.CreateTargetGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTargetGroup", arg0)
	ret0, _ := ret[0].(*elbv2.CreateTargetGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTargetGroup indicates an expected call of CreateTargetGroup.
func (mr *MockELBV2MockRecorder) CreateTargetGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTargetGroup", reflect.TypeOf((*MockELBV2)(nil).CreateTargetGroup), arg0)
}

// CreateTargetGroupRequest mocks base method.
func (m *MockELBV2) CreateTargetGroupRequest(arg0 *elbv2.CreateTargetGroupInput) (*request.Request, *elbv2.CreateTargetGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTargetGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.CreateTargetGroupOutput)
	return ret0, ret1
}

// CreateTargetGroupRequest indicates an expected call of CreateTargetGroupRequest.
func (mr *MockELBV2MockRecorder) CreateTargetGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTargetGroupRequest", reflect.TypeOf((*MockELBV2)(nil).CreateTargetGroupRequest), arg0)
}

// CreateTargetGroupWithContext mocks base method.
func (m *MockELBV2) CreateTargetGroupWithContext(arg0 context.Context, arg1 *elbv2.CreateTargetGroupInput, arg2 ...request.Option) (*elbv2.CreateTargetGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTargetGroupWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.CreateTargetGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTargetGroupWithContext indicates an expected call of CreateTargetGroupWithContext.
func (mr *MockELBV2MockRecorder) CreateTargetGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTargetGroupWithContext", reflect.TypeOf((*MockELBV2)(nil).CreateTargetGroupWithContext), varargs...)
}

// DeleteListener mocks base method.
func (m *MockELBV2) DeleteListener(arg0 *elbv2.DeleteListenerInput) (*elbv2.DeleteListenerOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteListener", arg0)
	ret0, _ := ret[0].(*elbv2.DeleteListenerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteListener indicates an expected call of DeleteListener.
func (mr *MockELBV2MockRecorder) DeleteListener(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteListener", reflect.TypeOf((*MockELBV2)(nil).DeleteListener), arg0)
}

// DeleteListenerRequest mocks base method.
func (m *MockELBV2) DeleteListenerRequest(arg0 *elbv2.DeleteListenerInput) (*request.Request, *elbv2.DeleteListenerOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteListenerRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DeleteListenerOutput)
	return ret0, ret1
}

// DeleteListenerRequest indicates an expected call of DeleteListenerRequest.
func (mr *MockELBV2MockRecorder) DeleteListenerRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteListenerRequest", reflect.TypeOf((*MockELBV2)(nil).DeleteListenerRequest), arg0)
}

// DeleteListenerWithContext mocks base method.
func (m *MockELBV2) DeleteListenerWithContext(arg0 context.Context, arg1 *elbv2.DeleteListenerInput, arg2 ...request.Option) (*elbv2.DeleteListenerOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteListenerWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DeleteListenerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteListenerWithContext indicates an expected call of DeleteListenerWithContext.
func (mr *MockELBV2MockRecorder) DeleteListenerWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteListenerWithContext", reflect.TypeOf((*MockELBV2)(nil).DeleteListenerWithContext), varargs...)
}

// DeleteLoadBalancer mocks base method.
func (m *MockELBV2) DeleteLoadBalancer(arg0 *elbv2.DeleteLoadBalancerInput) (*elbv2.DeleteLoadBalancerOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLoadBalancer", arg0)
	ret0, _ := ret[0].(*elbv2.DeleteLoadBalancerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLoadBalancer indicates an expected call of DeleteLoadBalancer.
func (mr *MockELBV2MockRecorder) DeleteLoadBalancer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLoadBalancer", reflect.TypeOf((*MockELBV2)(nil).DeleteLoadBalancer), arg0)
}

// DeleteLoadBalancerRequest mocks base method.
func (m *MockELBV2) DeleteLoadBalancerRequest(arg0 *elbv2.DeleteLoadBalancerInput) (*request.Request, *elbv2.DeleteLoadBalancerOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLoadBalancerRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DeleteLoadBalancerOutput)
	return ret0, ret1
}

// DeleteLoadBalancerRequest indicates an expected call of DeleteLoadBalancerRequest.
func (mr *MockELBV2MockRecorder) DeleteLoadBalancerRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLoadBalancerRequest", reflect.TypeOf((*MockELBV2)(nil).DeleteLoadBalancerRequest), arg0)
}

// DeleteLoadBalancerWithContext mocks base method.
func (m *MockELBV2) DeleteLoadBalancerWithContext(arg0 context.Context, arg1 *elbv2.DeleteLoadBalancerInput, arg2 ...request.Option) (*elbv2.DeleteLoadBalancerOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteLoadBalancerWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DeleteLoadBalancerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLoadBalancerWithContext indicates an expected call of DeleteLoadBalancerWithContext.
func (mr *MockELBV2MockRecorder) DeleteLoadBalancerWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLoadBalancerWithContext", reflect.TypeOf((*MockELBV2)(nil).DeleteLoadBalancerWithContext), varargs...)
}

// DeleteRule mocks base method.
func (m *MockELBV2) DeleteRule(arg0 *elbv2.DeleteRuleInput) (*elbv2.DeleteRuleOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRule", arg0)
	ret0, _ := ret[0].(*elbv2.DeleteRuleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRule indicates an expected call of DeleteRule.
func (mr *MockELBV2MockRecorder) DeleteRule(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRule", reflect.TypeOf((*MockELBV2)(nil).DeleteRule), arg0)
}

// DeleteRuleRequest mocks base method.
func (m *MockELBV2) DeleteRuleRequest(arg0 *elbv2.DeleteRuleInput) (*request.Request, *elbv2.DeleteRuleOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRuleRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DeleteRuleOutput)
	return ret0, ret1
}

// DeleteRuleRequest indicates an expected call of DeleteRuleRequest.
func (mr *MockELBV2MockRecorder) DeleteRuleRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRuleRequest", reflect.TypeOf((*MockELBV2)(nil).DeleteRuleRequest), arg0)
}

// DeleteRuleWithContext mocks base method.
func (m *MockELBV2) DeleteRuleWithContext(arg0 context.Context, arg1 *elbv2.DeleteRuleInput, arg2 ...request.Option) (*elbv2.DeleteRuleOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRuleWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DeleteRuleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRuleWithContext indicates an expected call of DeleteRuleWithContext.
func (mr *MockELBV2MockRecorder) DeleteRuleWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRuleWithContext", reflect.TypeOf((*MockELBV2)(nil).DeleteRuleWithContext), varargs...)
}

// DeleteTargetGroup mocks base method.
func (m *MockELBV2) DeleteTargetGroup(arg0 *elbv2.DeleteTargetGroupInput) (*elbv2.DeleteTargetGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTargetGroup", arg0)
	ret0, _ := ret[0].(*elbv2.DeleteTargetGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTargetGroup indicates an expected call of DeleteTargetGroup.
func (mr *MockELBV2MockRecorder) DeleteTargetGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTargetGroup", reflect.TypeOf((*MockELBV2)(nil).DeleteTargetGroup), arg0)
}

// DeleteTargetGroupRequest mocks base method.
func (m *MockELBV2) DeleteTargetGroupRequest(arg0 *elbv2.DeleteTargetGroupInput) (*request.Request, *elbv2.DeleteTargetGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTargetGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DeleteTargetGroupOutput)
	return ret0, ret1
}

// DeleteTargetGroupRequest indicates an expected call of DeleteTargetGroupRequest.
func (mr *MockELBV2MockRecorder) DeleteTargetGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTargetGroupRequest", reflect.TypeOf((*MockELBV2)(nil).DeleteTargetGroupRequest), arg0)
}

// DeleteTargetGroupWithContext mocks base method.
func (m *MockELBV2) DeleteTargetGroupWithContext(arg0 context.Context, arg1 *elbv2.DeleteTargetGroupInput, arg2 ...request.Option) (*elbv2.DeleteTargetGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTargetGroupWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DeleteTargetGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTargetGroupWithContext indicates an expected call of DeleteTargetGroupWithContext.
func (mr *MockELBV2MockRecorder) DeleteTargetGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTargetGroupWithContext", reflect.TypeOf((*MockELBV2)(nil).DeleteTargetGroupWithContext), varargs...)
}

// DeregisterTargets mocks base method.
func (m *MockELBV2) DeregisterTargets(arg0 *elbv2.DeregisterTargetsInput) (*elbv2.DeregisterTargetsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeregisterTargets", arg0)
	ret0, _ := ret[0].(*elbv2.DeregisterTargetsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeregisterTargets indicates an expected call of DeregisterTargets.
func (mr *MockELBV2MockRecorder) DeregisterTargets(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeregisterTargets", reflect.TypeOf((*MockELBV2)(nil).DeregisterTargets), arg0)
}

// DeregisterTargetsRequest mocks base method.
func (m *MockELBV2) DeregisterTargetsRequest(arg0 *elbv2.DeregisterTargetsInput) (*request.Request, *elbv2.DeregisterTargetsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeregisterTargetsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DeregisterTargetsOutput)
	return ret0, ret1
}

// DeregisterTargetsRequest indicates an expected call of DeregisterTargetsRequest.
func (mr *MockELBV2MockRecorder) DeregisterTargetsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeregisterTargetsRequest", reflect.TypeOf((*MockELBV2)(nil).DeregisterTargetsRequest), arg0)
}

// DeregisterTargetsWithContext mocks base method.
func (m *MockELBV2) DeregisterTargetsWithContext(arg0 context.Context, arg1 *elbv2.DeregisterTargetsInput, arg2 ...request.Option) (*elbv2.DeregisterTargetsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeregisterTargetsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DeregisterTargetsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeregisterTargetsWithContext indicates an expected call of DeregisterTargetsWithContext.
func (mr *MockELBV2MockRecorder) DeregisterTargetsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeregisterTargetsWithContext", reflect.TypeOf((*MockELBV2)(nil).DeregisterTargetsWithContext), varargs...)
}

// DescribeAccountLimits mocks base method.
func (m *MockELBV2) DescribeAccountLimits(arg0 *elbv2.DescribeAccountLimitsInput) (*elbv2.DescribeAccountLimitsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAccountLimits", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeAccountLimitsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAccountLimits indicates an expected call of DescribeAccountLimits.
func (mr *MockELBV2MockRecorder) DescribeAccountLimits(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAccountLimits", reflect.TypeOf((*MockELBV2)(nil).DescribeAccountLimits), arg0)
}

// DescribeAccountLimitsRequest mocks base method.
func (m *MockELBV2) DescribeAccountLimitsRequest(arg0 *elbv2.DescribeAccountLimitsInput) (*request.Request, *elbv2.DescribeAccountLimitsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAccountLimitsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeAccountLimitsOutput)
	return ret0, ret1
}

// DescribeAccountLimitsRequest indicates an expected call of DescribeAccountLimitsRequest.
func (mr *MockELBV2MockRecorder) DescribeAccountLimitsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAccountLimitsRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeAccountLimitsRequest), arg0)
}

// DescribeAccountLimitsWithContext mocks base method.
func (m *MockELBV2) DescribeAccountLimitsWithContext(arg0 context.Context, arg1 *elbv2.DescribeAccountLimitsInput, arg2 ...request.Option) (*elbv2.DescribeAccountLimitsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeAccountLimitsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeAccountLimitsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAccountLimitsWithContext indicates an expected call of DescribeAccountLimitsWithContext.
func (mr *MockELBV2MockRecorder) DescribeAccountLimitsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAccountLimitsWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeAccountLimitsWithContext), varargs...)
}

// DescribeListenerCertificates mocks base method.
func (m *MockELBV2) DescribeListenerCertificates(arg0 *elbv2.DescribeListenerCertificatesInput) (*elbv2.DescribeListenerCertificatesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListenerCertificates", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeListenerCertificatesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeListenerCertificates indicates an expected call of DescribeListenerCertificates.
func (mr *MockELBV2MockRecorder) DescribeListenerCertificates(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenerCertificates", reflect.TypeOf((*MockELBV2)(nil).DescribeListenerCertificates), arg0)
}

// DescribeListenerCertificatesAsList mocks base method.
func (m *MockELBV2) DescribeListenerCertificatesAsList(arg0 context.Context, arg1 *elbv2.DescribeListenerCertificatesInput) ([]*elbv2.Certificate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListenerCertificatesAsList", arg0, arg1)
	ret0, _ := ret[0].([]*elbv2.Certificate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeListenerCertificatesAsList indicates an expected call of DescribeListenerCertificatesAsList.
func (mr *MockELBV2MockRecorder) DescribeListenerCertificatesAsList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenerCertificatesAsList", reflect.TypeOf((*MockELBV2)(nil).DescribeListenerCertificatesAsList), arg0, arg1)
}

// DescribeListenerCertificatesRequest mocks base method.
func (m *MockELBV2) DescribeListenerCertificatesRequest(arg0 *elbv2.DescribeListenerCertificatesInput) (*request.Request, *elbv2.DescribeListenerCertificatesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListenerCertificatesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeListenerCertificatesOutput)
	return ret0, ret1
}

// DescribeListenerCertificatesRequest indicates an expected call of DescribeListenerCertificatesRequest.
func (mr *MockELBV2MockRecorder) DescribeListenerCertificatesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenerCertificatesRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeListenerCertificatesRequest), arg0)
}

// DescribeListenerCertificatesWithContext mocks base method.
func (m *MockELBV2) DescribeListenerCertificatesWithContext(arg0 context.Context, arg1 *elbv2.DescribeListenerCertificatesInput, arg2 ...request.Option) (*elbv2.DescribeListenerCertificatesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeListenerCertificatesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeListenerCertificatesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeListenerCertificatesWithContext indicates an expected call of DescribeListenerCertificatesWithContext.
func (mr *MockELBV2MockRecorder) DescribeListenerCertificatesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenerCertificatesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeListenerCertificatesWithContext), varargs...)
}

// DescribeListeners mocks base method.
func (m *MockELBV2) DescribeListeners(arg0 *elbv2.DescribeListenersInput) (*elbv2.DescribeListenersOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListeners", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeListenersOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeListeners indicates an expected call of DescribeListeners.
func (mr *MockELBV2MockRecorder) DescribeListeners(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListeners", reflect.TypeOf((*MockELBV2)(nil).DescribeListeners), arg0)
}

// DescribeListenersAsList mocks base method.
func (m *MockELBV2) DescribeListenersAsList(arg0 context.Context, arg1 *elbv2.DescribeListenersInput) ([]*elbv2.Listener, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListenersAsList", arg0, arg1)
	ret0, _ := ret[0].([]*elbv2.Listener)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeListenersAsList indicates an expected call of DescribeListenersAsList.
func (mr *MockELBV2MockRecorder) DescribeListenersAsList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenersAsList", reflect.TypeOf((*MockELBV2)(nil).DescribeListenersAsList), arg0, arg1)
}

// DescribeListenersPages mocks base method.
func (m *MockELBV2) DescribeListenersPages(arg0 *elbv2.DescribeListenersInput, arg1 func(*elbv2.DescribeListenersOutput, bool) bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListenersPages", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DescribeListenersPages indicates an expected call of DescribeListenersPages.
func (mr *MockELBV2MockRecorder) DescribeListenersPages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenersPages", reflect.TypeOf((*MockELBV2)(nil).DescribeListenersPages), arg0, arg1)
}

// DescribeListenersPagesWithContext mocks base method.
func (m *MockELBV2) DescribeListenersPagesWithContext(arg0 context.Context, arg1 *elbv2.DescribeListenersInput, arg2 func(*elbv2.DescribeListenersOutput, bool) bool, arg3 ...request.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeListenersPagesWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DescribeListenersPagesWithContext indicates an expected call of DescribeListenersPagesWithContext.
func (mr *MockELBV2MockRecorder) DescribeListenersPagesWithContext(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenersPagesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeListenersPagesWithContext), varargs...)
}

// DescribeListenersRequest mocks base method.
func (m *MockELBV2) DescribeListenersRequest(arg0 *elbv2.DescribeListenersInput) (*request.Request, *elbv2.DescribeListenersOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeListenersRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeListenersOutput)
	return ret0, ret1
}

// DescribeListenersRequest indicates an expected call of DescribeListenersRequest.
func (mr *MockELBV2MockRecorder) DescribeListenersRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenersRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeListenersRequest), arg0)
}

// DescribeListenersWithContext mocks base method.
func (m *MockELBV2) DescribeListenersWithContext(arg0 context.Context, arg1 *elbv2.DescribeListenersInput, arg2 ...request.Option) (*elbv2.DescribeListenersOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeListenersWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeListenersOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeListenersWithContext indicates an expected call of DescribeListenersWithContext.
func (mr *MockELBV2MockRecorder) DescribeListenersWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeListenersWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeListenersWithContext), varargs...)
}

// DescribeLoadBalancerAttributes mocks base method.
func (m *MockELBV2) DescribeLoadBalancerAttributes(arg0 *elbv2.DescribeLoadBalancerAttributesInput) (*elbv2.DescribeLoadBalancerAttributesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancerAttributes", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeLoadBalancerAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancerAttributes indicates an expected call of DescribeLoadBalancerAttributes.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancerAttributes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancerAttributes", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancerAttributes), arg0)
}

// DescribeLoadBalancerAttributesRequest mocks base method.
func (m *MockELBV2) DescribeLoadBalancerAttributesRequest(arg0 *elbv2.DescribeLoadBalancerAttributesInput) (*request.Request, *elbv2.DescribeLoadBalancerAttributesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancerAttributesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeLoadBalancerAttributesOutput)
	return ret0, ret1
}

// DescribeLoadBalancerAttributesRequest indicates an expected call of DescribeLoadBalancerAttributesRequest.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancerAttributesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancerAttributesRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancerAttributesRequest), arg0)
}

// DescribeLoadBalancerAttributesWithContext mocks base method.
func (m *MockELBV2) DescribeLoadBalancerAttributesWithContext(arg0 context.Context, arg1 *elbv2.DescribeLoadBalancerAttributesInput, arg2 ...request.Option) (*elbv2.DescribeLoadBalancerAttributesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeLoadBalancerAttributesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeLoadBalancerAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancerAttributesWithContext indicates an expected call of DescribeLoadBalancerAttributesWithContext.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancerAttributesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancerAttributesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancerAttributesWithContext), varargs...)
}

// DescribeLoadBalancers mocks base method.
func (m *MockELBV2) DescribeLoadBalancers(arg0 *elbv2.DescribeLoadBalancersInput) (*elbv2.DescribeLoadBalancersOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancers", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeLoadBalancersOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancers indicates an expected call of DescribeLoadBalancers.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancers(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancers", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancers), arg0)
}

// DescribeLoadBalancersAsList mocks base method.
func (m *MockELBV2) DescribeLoadBalancersAsList(arg0 context.Context, arg1 *elbv2.DescribeLoadBalancersInput) ([]*elbv2.LoadBalancer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancersAsList", arg0, arg1)
	ret0, _ := ret[0].([]*elbv2.LoadBalancer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancersAsList indicates an expected call of DescribeLoadBalancersAsList.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancersAsList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancersAsList", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancersAsList), arg0, arg1)
}

// DescribeLoadBalancersPages mocks base method.
func (m *MockELBV2) DescribeLoadBalancersPages(arg0 *elbv2.DescribeLoadBalancersInput, arg1 func(*elbv2.DescribeLoadBalancersOutput, bool) bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancersPages", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DescribeLoadBalancersPages indicates an expected call of DescribeLoadBalancersPages.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancersPages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancersPages", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancersPages), arg0, arg1)
}

// DescribeLoadBalancersPagesWithContext mocks base method.
func (m *MockELBV2) DescribeLoadBalancersPagesWithContext(arg0 context.Context, arg1 *elbv2.DescribeLoadBalancersInput, arg2 func(*elbv2.DescribeLoadBalancersOutput, bool) bool, arg3 ...request.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeLoadBalancersPagesWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DescribeLoadBalancersPagesWithContext indicates an expected call of DescribeLoadBalancersPagesWithContext.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancersPagesWithContext(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancersPagesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancersPagesWithContext), varargs...)
}

// DescribeLoadBalancersRequest mocks base method.
func (m *MockELBV2) DescribeLoadBalancersRequest(arg0 *elbv2.DescribeLoadBalancersInput) (*request.Request, *elbv2.DescribeLoadBalancersOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLoadBalancersRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeLoadBalancersOutput)
	return ret0, ret1
}

// DescribeLoadBalancersRequest indicates an expected call of DescribeLoadBalancersRequest.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancersRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancersRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancersRequest), arg0)
}

// DescribeLoadBalancersWithContext mocks base method.
func (m *MockELBV2) DescribeLoadBalancersWithContext(arg0 context.Context, arg1 *elbv2.DescribeLoadBalancersInput, arg2 ...request.Option) (*elbv2.DescribeLoadBalancersOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeLoadBalancersWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeLoadBalancersOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLoadBalancersWithContext indicates an expected call of DescribeLoadBalancersWithContext.
func (mr *MockELBV2MockRecorder) DescribeLoadBalancersWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLoadBalancersWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeLoadBalancersWithContext), varargs...)
}

// DescribeRules mocks base method.
func (m *MockELBV2) DescribeRules(arg0 *elbv2.DescribeRulesInput) (*elbv2.DescribeRulesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeRules", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeRulesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeRules indicates an expected call of DescribeRules.
func (mr *MockELBV2MockRecorder) DescribeRules(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeRules", reflect.TypeOf((*MockELBV2)(nil).DescribeRules), arg0)
}

// DescribeRulesAsList mocks base method.
func (m *MockELBV2) DescribeRulesAsList(arg0 context.Context, arg1 *elbv2.DescribeRulesInput) ([]*elbv2.Rule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeRulesAsList", arg0, arg1)
	ret0, _ := ret[0].([]*elbv2.Rule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeRulesAsList indicates an expected call of DescribeRulesAsList.
func (mr *MockELBV2MockRecorder) DescribeRulesAsList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeRulesAsList", reflect.TypeOf((*MockELBV2)(nil).DescribeRulesAsList), arg0, arg1)
}

// DescribeRulesRequest mocks base method.
func (m *MockELBV2) DescribeRulesRequest(arg0 *elbv2.DescribeRulesInput) (*request.Request, *elbv2.DescribeRulesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeRulesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeRulesOutput)
	return ret0, ret1
}

// DescribeRulesRequest indicates an expected call of DescribeRulesRequest.
func (mr *MockELBV2MockRecorder) DescribeRulesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeRulesRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeRulesRequest), arg0)
}

// DescribeRulesWithContext mocks base method.
func (m *MockELBV2) DescribeRulesWithContext(arg0 context.Context, arg1 *elbv2.DescribeRulesInput, arg2 ...request.Option) (*elbv2.DescribeRulesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeRulesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeRulesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeRulesWithContext indicates an expected call of DescribeRulesWithContext.
func (mr *MockELBV2MockRecorder) DescribeRulesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeRulesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeRulesWithContext), varargs...)
}

// DescribeSSLPolicies mocks base method.
func (m *MockELBV2) DescribeSSLPolicies(arg0 *elbv2.DescribeSSLPoliciesInput) (*elbv2.DescribeSSLPoliciesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSSLPolicies", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeSSLPoliciesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSSLPolicies indicates an expected call of DescribeSSLPolicies.
func (mr *MockELBV2MockRecorder) DescribeSSLPolicies(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSSLPolicies", reflect.TypeOf((*MockELBV2)(nil).DescribeSSLPolicies), arg0)
}

// DescribeSSLPoliciesRequest mocks base method.
func (m *MockELBV2) DescribeSSLPoliciesRequest(arg0 *elbv2.DescribeSSLPoliciesInput) (*request.Request, *elbv2.DescribeSSLPoliciesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSSLPoliciesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeSSLPoliciesOutput)
	return ret0, ret1
}

// DescribeSSLPoliciesRequest indicates an expected call of DescribeSSLPoliciesRequest.
func (mr *MockELBV2MockRecorder) DescribeSSLPoliciesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSSLPoliciesRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeSSLPoliciesRequest), arg0)
}

// DescribeSSLPoliciesWithContext mocks base method.
func (m *MockELBV2) DescribeSSLPoliciesWithContext(arg0 context.Context, arg1 *elbv2.DescribeSSLPoliciesInput, arg2 ...request.Option) (*elbv2.DescribeSSLPoliciesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeSSLPoliciesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeSSLPoliciesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSSLPoliciesWithContext indicates an expected call of DescribeSSLPoliciesWithContext.
func (mr *MockELBV2MockRecorder) DescribeSSLPoliciesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSSLPoliciesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeSSLPoliciesWithContext), varargs...)
}

// DescribeTags mocks base method.
func (m *MockELBV2) DescribeTags(arg0 *elbv2.DescribeTagsInput) (*elbv2.DescribeTagsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTags", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeTagsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTags indicates an expected call of DescribeTags.
func (mr *MockELBV2MockRecorder) DescribeTags(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTags", reflect.TypeOf((*MockELBV2)(nil).DescribeTags), arg0)
}

// DescribeTagsRequest mocks base method.
func (m *MockELBV2) DescribeTagsRequest(arg0 *elbv2.DescribeTagsInput) (*request.Request, *elbv2.DescribeTagsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTagsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeTagsOutput)
	return ret0, ret1
}

// DescribeTagsRequest indicates an expected call of DescribeTagsRequest.
func (mr *MockELBV2MockRecorder) DescribeTagsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTagsRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeTagsRequest), arg0)
}

// DescribeTagsWithContext mocks base method.
func (m *MockELBV2) DescribeTagsWithContext(arg0 context.Context, arg1 *elbv2.DescribeTagsInput, arg2 ...request.Option) (*elbv2.DescribeTagsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeTagsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeTagsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTagsWithContext indicates an expected call of DescribeTagsWithContext.
func (mr *MockELBV2MockRecorder) DescribeTagsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTagsWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeTagsWithContext), varargs...)
}

// DescribeTargetGroupAttributes mocks base method.
func (m *MockELBV2) DescribeTargetGroupAttributes(arg0 *elbv2.DescribeTargetGroupAttributesInput) (*elbv2.DescribeTargetGroupAttributesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetGroupAttributes", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeTargetGroupAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTargetGroupAttributes indicates an expected call of DescribeTargetGroupAttributes.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupAttributes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupAttributes", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupAttributes), arg0)
}

// DescribeTargetGroupAttributesRequest mocks base method.
func (m *MockELBV2) DescribeTargetGroupAttributesRequest(arg0 *elbv2.DescribeTargetGroupAttributesInput) (*request.Request, *elbv2.DescribeTargetGroupAttributesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetGroupAttributesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeTargetGroupAttributesOutput)
	return ret0, ret1
}

// DescribeTargetGroupAttributesRequest indicates an expected call of DescribeTargetGroupAttributesRequest.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupAttributesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupAttributesRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupAttributesRequest), arg0)
}

// DescribeTargetGroupAttributesWithContext mocks base method.
func (m *MockELBV2) DescribeTargetGroupAttributesWithContext(arg0 context.Context, arg1 *elbv2.DescribeTargetGroupAttributesInput, arg2 ...request.Option) (*elbv2.DescribeTargetGroupAttributesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeTargetGroupAttributesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeTargetGroupAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTargetGroupAttributesWithContext indicates an expected call of DescribeTargetGroupAttributesWithContext.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupAttributesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupAttributesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupAttributesWithContext), varargs...)
}

// DescribeTargetGroups mocks base method.
func (m *MockELBV2) DescribeTargetGroups(arg0 *elbv2.DescribeTargetGroupsInput) (*elbv2.DescribeTargetGroupsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetGroups", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeTargetGroupsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTargetGroups indicates an expected call of DescribeTargetGroups.
func (mr *MockELBV2MockRecorder) DescribeTargetGroups(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroups", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroups), arg0)
}

// DescribeTargetGroupsAsList mocks base method.
func (m *MockELBV2) DescribeTargetGroupsAsList(arg0 context.Context, arg1 *elbv2.DescribeTargetGroupsInput) ([]*elbv2.TargetGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetGroupsAsList", arg0, arg1)
	ret0, _ := ret[0].([]*elbv2.TargetGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTargetGroupsAsList indicates an expected call of DescribeTargetGroupsAsList.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupsAsList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupsAsList", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupsAsList), arg0, arg1)
}

// DescribeTargetGroupsPages mocks base method.
func (m *MockELBV2) DescribeTargetGroupsPages(arg0 *elbv2.DescribeTargetGroupsInput, arg1 func(*elbv2.DescribeTargetGroupsOutput, bool) bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetGroupsPages", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DescribeTargetGroupsPages indicates an expected call of DescribeTargetGroupsPages.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupsPages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupsPages", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupsPages), arg0, arg1)
}

// DescribeTargetGroupsPagesWithContext mocks base method.
func (m *MockELBV2) DescribeTargetGroupsPagesWithContext(arg0 context.Context, arg1 *elbv2.DescribeTargetGroupsInput, arg2 func(*elbv2.DescribeTargetGroupsOutput, bool) bool, arg3 ...request.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeTargetGroupsPagesWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DescribeTargetGroupsPagesWithContext indicates an expected call of DescribeTargetGroupsPagesWithContext.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupsPagesWithContext(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupsPagesWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupsPagesWithContext), varargs...)
}

// DescribeTargetGroupsRequest mocks base method.
func (m *MockELBV2) DescribeTargetGroupsRequest(arg0 *elbv2.DescribeTargetGroupsInput) (*request.Request, *elbv2.DescribeTargetGroupsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetGroupsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeTargetGroupsOutput)
	return ret0, ret1
}

// DescribeTargetGroupsRequest indicates an expected call of DescribeTargetGroupsRequest.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupsRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupsRequest), arg0)
}

// DescribeTargetGroupsWithContext mocks base method.
func (m *MockELBV2) DescribeTargetGroupsWithContext(arg0 context.Context, arg1 *elbv2.DescribeTargetGroupsInput, arg2 ...request.Option) (*elbv2.DescribeTargetGroupsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeTargetGroupsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeTargetGroupsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTargetGroupsWithContext indicates an expected call of DescribeTargetGroupsWithContext.
func (mr *MockELBV2MockRecorder) DescribeTargetGroupsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetGroupsWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetGroupsWithContext), varargs...)
}

// DescribeTargetHealth mocks base method.
func (m *MockELBV2) DescribeTargetHealth(arg0 *elbv2.DescribeTargetHealthInput) (*elbv2.DescribeTargetHealthOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetHealth", arg0)
	ret0, _ := ret[0].(*elbv2.DescribeTargetHealthOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTargetHealth indicates an expected call of DescribeTargetHealth.
func (mr *MockELBV2MockRecorder) DescribeTargetHealth(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetHealth", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetHealth), arg0)
}

// DescribeTargetHealthRequest mocks base method.
func (m *MockELBV2) DescribeTargetHealthRequest(arg0 *elbv2.DescribeTargetHealthInput) (*request.Request, *elbv2.DescribeTargetHealthOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTargetHealthRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.DescribeTargetHealthOutput)
	return ret0, ret1
}

// DescribeTargetHealthRequest indicates an expected call of DescribeTargetHealthRequest.
func (mr *MockELBV2MockRecorder) DescribeTargetHealthRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetHealthRequest", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetHealthRequest), arg0)
}

// DescribeTargetHealthWithContext mocks base method.
func (m *MockELBV2) DescribeTargetHealthWithContext(arg0 context.Context, arg1 *elbv2.DescribeTargetHealthInput, arg2 ...request.Option) (*elbv2.DescribeTargetHealthOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeTargetHealthWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.DescribeTargetHealthOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTargetHealthWithContext indicates an expected call of DescribeTargetHealthWithContext.
func (mr *MockELBV2MockRecorder) DescribeTargetHealthWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTargetHealthWithContext", reflect.TypeOf((*MockELBV2)(nil).DescribeTargetHealthWithContext), varargs...)
}

// ModifyListener mocks base method.
func (m *MockELBV2) ModifyListener(arg0 *elbv2.ModifyListenerInput) (*elbv2.ModifyListenerOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyListener", arg0)
	ret0, _ := ret[0].(*elbv2.ModifyListenerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyListener indicates an expected call of ModifyListener.
func (mr *MockELBV2MockRecorder) ModifyListener(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyListener", reflect.TypeOf((*MockELBV2)(nil).ModifyListener), arg0)
}

// ModifyListenerRequest mocks base method.
func (m *MockELBV2) ModifyListenerRequest(arg0 *elbv2.ModifyListenerInput) (*request.Request, *elbv2.ModifyListenerOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyListenerRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.ModifyListenerOutput)
	return ret0, ret1
}

// ModifyListenerRequest indicates an expected call of ModifyListenerRequest.
func (mr *MockELBV2MockRecorder) ModifyListenerRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyListenerRequest", reflect.TypeOf((*MockELBV2)(nil).ModifyListenerRequest), arg0)
}

// ModifyListenerWithContext mocks base method.
func (m *MockELBV2) ModifyListenerWithContext(arg0 context.Context, arg1 *elbv2.ModifyListenerInput, arg2 ...request.Option) (*elbv2.ModifyListenerOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyListenerWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.ModifyListenerOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyListenerWithContext indicates an expected call of ModifyListenerWithContext.
func (mr *MockELBV2MockRecorder) ModifyListenerWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyListenerWithContext", reflect.TypeOf((*MockELBV2)(nil).ModifyListenerWithContext), varargs...)
}

// ModifyLoadBalancerAttributes mocks base method.
func (m *MockELBV2) ModifyLoadBalancerAttributes(arg0 *elbv2.ModifyLoadBalancerAttributesInput) (*elbv2.ModifyLoadBalancerAttributesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyLoadBalancerAttributes", arg0)
	ret0, _ := ret[0].(*elbv2.ModifyLoadBalancerAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyLoadBalancerAttributes indicates an expected call of ModifyLoadBalancerAttributes.
func (mr *MockELBV2MockRecorder) ModifyLoadBalancerAttributes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyLoadBalancerAttributes", reflect.TypeOf((*MockELBV2)(nil).ModifyLoadBalancerAttributes), arg0)
}

// ModifyLoadBalancerAttributesRequest mocks base method.
func (m *MockELBV2) ModifyLoadBalancerAttributesRequest(arg0 *elbv2.ModifyLoadBalancerAttributesInput) (*request.Request, *elbv2.ModifyLoadBalancerAttributesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyLoadBalancerAttributesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.ModifyLoadBalancerAttributesOutput)
	return ret0, ret1
}

// ModifyLoadBalancerAttributesRequest indicates an expected call of ModifyLoadBalancerAttributesRequest.
func (mr *MockELBV2MockRecorder) ModifyLoadBalancerAttributesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyLoadBalancerAttributesRequest", reflect.TypeOf((*MockELBV2)(nil).ModifyLoadBalancerAttributesRequest), arg0)
}

// ModifyLoadBalancerAttributesWithContext mocks base method.
func (m *MockELBV2) ModifyLoadBalancerAttributesWithContext(arg0 context.Context, arg1 *elbv2.ModifyLoadBalancerAttributesInput, arg2 ...request.Option) (*elbv2.ModifyLoadBalancerAttributesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyLoadBalancerAttributesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.ModifyLoadBalancerAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyLoadBalancerAttributesWithContext indicates an expected call of ModifyLoadBalancerAttributesWithContext.
func (mr *MockELBV2MockRecorder) ModifyLoadBalancerAttributesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyLoadBalancerAttributesWithContext", reflect.TypeOf((*MockELBV2)(nil).ModifyLoadBalancerAttributesWithContext), varargs...)
}

// ModifyRule mocks base method.
func (m *MockELBV2) ModifyRule(arg0 *elbv2.ModifyRuleInput) (*elbv2.ModifyRuleOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyRule", arg0)
	ret0, _ := ret[0].(*elbv2.ModifyRuleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyRule indicates an expected call of ModifyRule.
func (mr *MockELBV2MockRecorder) ModifyRule(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyRule", reflect.TypeOf((*MockELBV2)(nil).ModifyRule), arg0)
}

// ModifyRuleRequest mocks base method.
func (m *MockELBV2) ModifyRuleRequest(arg0 *elbv2.ModifyRuleInput) (*request.Request, *elbv2.ModifyRuleOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyRuleRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.ModifyRuleOutput)
	return ret0, ret1
}

// ModifyRuleRequest indicates an expected call of ModifyRuleRequest.
func (mr *MockELBV2MockRecorder) ModifyRuleRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyRuleRequest", reflect.TypeOf((*MockELBV2)(nil).ModifyRuleRequest), arg0)
}

// ModifyRuleWithContext mocks base method.
func (m *MockELBV2) ModifyRuleWithContext(arg0 context.Context, arg1 *elbv2.ModifyRuleInput, arg2 ...request.Option) (*elbv2.ModifyRuleOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyRuleWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.ModifyRuleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyRuleWithContext indicates an expected call of ModifyRuleWithContext.
func (mr *MockELBV2MockRecorder) ModifyRuleWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyRuleWithContext", reflect.TypeOf((*MockELBV2)(nil).ModifyRuleWithContext), varargs...)
}

// ModifyTargetGroup mocks base method.
func (m *MockELBV2) ModifyTargetGroup(arg0 *elbv2.ModifyTargetGroupInput) (*elbv2.ModifyTargetGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyTargetGroup", arg0)
	ret0, _ := ret[0].(*elbv2.ModifyTargetGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyTargetGroup indicates an expected call of ModifyTargetGroup.
func (mr *MockELBV2MockRecorder) ModifyTargetGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTargetGroup", reflect.TypeOf((*MockELBV2)(nil).ModifyTargetGroup), arg0)
}

// ModifyTargetGroupAttributes mocks base method.
func (m *MockELBV2) ModifyTargetGroupAttributes(arg0 *elbv2.ModifyTargetGroupAttributesInput) (*elbv2.ModifyTargetGroupAttributesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyTargetGroupAttributes", arg0)
	ret0, _ := ret[0].(*elbv2.ModifyTargetGroupAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyTargetGroupAttributes indicates an expected call of ModifyTargetGroupAttributes.
func (mr *MockELBV2MockRecorder) ModifyTargetGroupAttributes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTargetGroupAttributes", reflect.TypeOf((*MockELBV2)(nil).ModifyTargetGroupAttributes), arg0)
}

// ModifyTargetGroupAttributesRequest mocks base method.
func (m *MockELBV2) ModifyTargetGroupAttributesRequest(arg0 *elbv2.ModifyTargetGroupAttributesInput) (*request.Request, *elbv2.ModifyTargetGroupAttributesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyTargetGroupAttributesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.ModifyTargetGroupAttributesOutput)
	return ret0, ret1
}

// ModifyTargetGroupAttributesRequest indicates an expected call of ModifyTargetGroupAttributesRequest.
func (mr *MockELBV2MockRecorder) ModifyTargetGroupAttributesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTargetGroupAttributesRequest", reflect.TypeOf((*MockELBV2)(nil).ModifyTargetGroupAttributesRequest), arg0)
}

// ModifyTargetGroupAttributesWithContext mocks base method.
func (m *MockELBV2) ModifyTargetGroupAttributesWithContext(arg0 context.Context, arg1 *elbv2.ModifyTargetGroupAttributesInput, arg2 ...request.Option) (*elbv2.ModifyTargetGroupAttributesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyTargetGroupAttributesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.ModifyTargetGroupAttributesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyTargetGroupAttributesWithContext indicates an expected call of ModifyTargetGroupAttributesWithContext.
func (mr *MockELBV2MockRecorder) ModifyTargetGroupAttributesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTargetGroupAttributesWithContext", reflect.TypeOf((*MockELBV2)(nil).ModifyTargetGroupAttributesWithContext), varargs...)
}

// ModifyTargetGroupRequest mocks base method.
func (m *MockELBV2) ModifyTargetGroupRequest(arg0 *elbv2.ModifyTargetGroupInput) (*request.Request, *elbv2.ModifyTargetGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyTargetGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.ModifyTargetGroupOutput)
	return ret0, ret1
}

// ModifyTargetGroupRequest indicates an expected call of ModifyTargetGroupRequest.
func (mr *MockELBV2MockRecorder) ModifyTargetGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTargetGroupRequest", reflect.TypeOf((*MockELBV2)(nil).ModifyTargetGroupRequest), arg0)
}

// ModifyTargetGroupWithContext mocks base method.
func (m *MockELBV2) ModifyTargetGroupWithContext(arg0 context.Context, arg1 *elbv2.ModifyTargetGroupInput, arg2 ...request.Option) (*elbv2.ModifyTargetGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyTargetGroupWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.ModifyTargetGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyTargetGroupWithContext indicates an expected call of ModifyTargetGroupWithContext.
func (mr *MockELBV2MockRecorder) ModifyTargetGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyTargetGroupWithContext", reflect.TypeOf((*MockELBV2)(nil).ModifyTargetGroupWithContext), varargs...)
}

// RegisterTargets mocks base method.
func (m *MockELBV2) RegisterTargets(arg0 *elbv2.RegisterTargetsInput) (*elbv2.RegisterTargetsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegisterTargets", arg0)
	ret0, _ := ret[0].(*elbv2.RegisterTargetsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegisterTargets indicates an expected call of RegisterTargets.
func (mr *MockELBV2MockRecorder) RegisterTargets(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterTargets", reflect.TypeOf((*MockELBV2)(nil).RegisterTargets), arg0)
}

// RegisterTargetsRequest mocks base method.
func (m *MockELBV2) RegisterTargetsRequest(arg0 *elbv2.RegisterTargetsInput) (*request.Request, *elbv2.RegisterTargetsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RegisterTargetsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.RegisterTargetsOutput)
	return ret0, ret1
}

// RegisterTargetsRequest indicates an expected call of RegisterTargetsRequest.
func (mr *MockELBV2MockRecorder) RegisterTargetsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterTargetsRequest", reflect.TypeOf((*MockELBV2)(nil).RegisterTargetsRequest), arg0)
}

// RegisterTargetsWithContext mocks base method.
func (m *MockELBV2) RegisterTargetsWithContext(arg0 context.Context, arg1 *elbv2.RegisterTargetsInput, arg2 ...request.Option) (*elbv2.RegisterTargetsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RegisterTargetsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.RegisterTargetsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RegisterTargetsWithContext indicates an expected call of RegisterTargetsWithContext.
func (mr *MockELBV2MockRecorder) RegisterTargetsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterTargetsWithContext", reflect.TypeOf((*MockELBV2)(nil).RegisterTargetsWithContext), varargs...)
}

// RemoveListenerCertificates mocks base method.
func (m *MockELBV2) RemoveListenerCertificates(arg0 *elbv2.RemoveListenerCertificatesInput) (*elbv2.RemoveListenerCertificatesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveListenerCertificates", arg0)
	ret0, _ := ret[0].(*elbv2.RemoveListenerCertificatesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveListenerCertificates indicates an expected call of RemoveListenerCertificates.
func (mr *MockELBV2MockRecorder) RemoveListenerCertificates(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveListenerCertificates", reflect.TypeOf((*MockELBV2)(nil).RemoveListenerCertificates), arg0)
}

// RemoveListenerCertificatesRequest mocks base method.
func (m *MockELBV2) RemoveListenerCertificatesRequest(arg0 *elbv2.RemoveListenerCertificatesInput) (*request.Request, *elbv2.RemoveListenerCertificatesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveListenerCertificatesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.RemoveListenerCertificatesOutput)
	return ret0, ret1
}

// RemoveListenerCertificatesRequest indicates an expected call of RemoveListenerCertificatesRequest.
func (mr *MockELBV2MockRecorder) RemoveListenerCertificatesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveListenerCertificatesRequest", reflect.TypeOf((*MockELBV2)(nil).RemoveListenerCertificatesRequest), arg0)
}

// RemoveListenerCertificatesWithContext mocks base method.
func (m *MockELBV2) RemoveListenerCertificatesWithContext(arg0 context.Context, arg1 *elbv2.RemoveListenerCertificatesInput, arg2 ...request.Option) (*elbv2.RemoveListenerCertificatesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveListenerCertificatesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.RemoveListenerCertificatesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveListenerCertificatesWithContext indicates an expected call of RemoveListenerCertificatesWithContext.
func (mr *MockELBV2MockRecorder) RemoveListenerCertificatesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveListenerCertificatesWithContext", reflect.TypeOf((*MockELBV2)(nil).RemoveListenerCertificatesWithContext), varargs...)
}

// RemoveTags mocks base method.
func (m *MockELBV2) RemoveTags(arg0 *elbv2.RemoveTagsInput) (*elbv2.RemoveTagsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveTags", arg0)
	ret0, _ := ret[0].(*elbv2.RemoveTagsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveTags indicates an expected call of RemoveTags.
func (mr *MockELBV2MockRecorder) RemoveTags(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveTags", reflect.TypeOf((*MockELBV2)(nil).RemoveTags), arg0)
}

// RemoveTagsRequest mocks base method.
func (m *MockELBV2) RemoveTagsRequest(arg0 *elbv2.RemoveTagsInput) (*request.Request, *elbv2.RemoveTagsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveTagsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.RemoveTagsOutput)
	return ret0, ret1
}

// RemoveTagsRequest indicates an expected call of RemoveTagsRequest.
func (mr *MockELBV2MockRecorder) RemoveTagsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveTagsRequest", reflect.TypeOf((*MockELBV2)(nil).RemoveTagsRequest), arg0)
}

// RemoveTagsWithContext mocks base method.
func (m *MockELBV2) RemoveTagsWithContext(arg0 context.Context, arg1 *elbv2.RemoveTagsInput, arg2 ...request.Option) (*elbv2.RemoveTagsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveTagsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.RemoveTagsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveTagsWithContext indicates an expected call of RemoveTagsWithContext.
func (mr *MockELBV2MockRecorder) RemoveTagsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveTagsWithContext", reflect.TypeOf((*MockELBV2)(nil).RemoveTagsWithContext), varargs...)
}

// SetIpAddressType mocks base method.
func (m *MockELBV2) SetIpAddressType(arg0 *elbv2.SetIpAddressTypeInput) (*elbv2.SetIpAddressTypeOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetIpAddressType", arg0)
	ret0, _ := ret[0].(*elbv2.SetIpAddressTypeOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetIpAddressType indicates an expected call of SetIpAddressType.
func (mr *MockELBV2MockRecorder) SetIpAddressType(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIpAddressType", reflect.TypeOf((*MockELBV2)(nil).SetIpAddressType), arg0)
}

// SetIpAddressTypeRequest mocks base method.
func (m *MockELBV2) SetIpAddressTypeRequest(arg0 *elbv2.SetIpAddressTypeInput) (*request.Request, *elbv2.SetIpAddressTypeOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetIpAddressTypeRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.SetIpAddressTypeOutput)
	return ret0, ret1
}

// SetIpAddressTypeRequest indicates an expected call of SetIpAddressTypeRequest.
func (mr *MockELBV2MockRecorder) SetIpAddressTypeRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIpAddressTypeRequest", reflect.TypeOf((*MockELBV2)(nil).SetIpAddressTypeRequest), arg0)
}

// SetIpAddressTypeWithContext mocks base method.
func (m *MockELBV2) SetIpAddressTypeWithContext(arg0 context.Context, arg1 *elbv2.SetIpAddressTypeInput, arg2 ...request.Option) (*elbv2.SetIpAddressTypeOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetIpAddressTypeWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.SetIpAddressTypeOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetIpAddressTypeWithContext indicates an expected call of SetIpAddressTypeWithContext.
func (mr *MockELBV2MockRecorder) SetIpAddressTypeWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIpAddressTypeWithContext", reflect.TypeOf((*MockELBV2)(nil).SetIpAddressTypeWithContext), varargs...)
}

// SetRulePriorities mocks base method.
func (m *MockELBV2) SetRulePriorities(arg0 *elbv2.SetRulePrioritiesInput) (*elbv2.SetRulePrioritiesOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRulePriorities", arg0)
	ret0, _ := ret[0].(*elbv2.SetRulePrioritiesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetRulePriorities indicates an expected call of SetRulePriorities.
func (mr *MockELBV2MockRecorder) SetRulePriorities(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRulePriorities", reflect.TypeOf((*MockELBV2)(nil).SetRulePriorities), arg0)
}

// SetRulePrioritiesRequest mocks base method.
func (m *MockELBV2) SetRulePrioritiesRequest(arg0 *elbv2.SetRulePrioritiesInput) (*request.Request, *elbv2.SetRulePrioritiesOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRulePrioritiesRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.SetRulePrioritiesOutput)
	return ret0, ret1
}

// SetRulePrioritiesRequest indicates an expected call of SetRulePrioritiesRequest.
func (mr *MockELBV2MockRecorder) SetRulePrioritiesRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRulePrioritiesRequest", reflect.TypeOf((*MockELBV2)(nil).SetRulePrioritiesRequest), arg0)
}

// SetRulePrioritiesWithContext mocks base method.
func (m *MockELBV2) SetRulePrioritiesWithContext(arg0 context.Context, arg1 *elbv2.SetRulePrioritiesInput, arg2 ...request.Option) (*elbv2.SetRulePrioritiesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetRulePrioritiesWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.SetRulePrioritiesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetRulePrioritiesWithContext indicates an expected call of SetRulePrioritiesWithContext.
func (mr *MockELBV2MockRecorder) SetRulePrioritiesWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRulePrioritiesWithContext", reflect.TypeOf((*MockELBV2)(nil).SetRulePrioritiesWithContext), varargs...)
}

// SetSecurityGroups mocks base method.
func (m *MockELBV2) SetSecurityGroups(arg0 *elbv2.SetSecurityGroupsInput) (*elbv2.SetSecurityGroupsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSecurityGroups", arg0)
	ret0, _ := ret[0].(*elbv2.SetSecurityGroupsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSecurityGroups indicates an expected call of SetSecurityGroups.
func (mr *MockELBV2MockRecorder) SetSecurityGroups(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSecurityGroups", reflect.TypeOf((*MockELBV2)(nil).SetSecurityGroups), arg0)
}

// SetSecurityGroupsRequest mocks base method.
func (m *MockELBV2) SetSecurityGroupsRequest(arg0 *elbv2.SetSecurityGroupsInput) (*request.Request, *elbv2.SetSecurityGroupsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSecurityGroupsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.SetSecurityGroupsOutput)
	return ret0, ret1
}

// SetSecurityGroupsRequest indicates an expected call of SetSecurityGroupsRequest.
func (mr *MockELBV2MockRecorder) SetSecurityGroupsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSecurityGroupsRequest", reflect.TypeOf((*MockELBV2)(nil).SetSecurityGroupsRequest), arg0)
}

// SetSecurityGroupsWithContext mocks base method.
func (m *MockELBV2) SetSecurityGroupsWithContext(arg0 context.Context, arg1 *elbv2.SetSecurityGroupsInput, arg2 ...request.Option) (*elbv2.SetSecurityGroupsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSecurityGroupsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.SetSecurityGroupsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSecurityGroupsWithContext indicates an expected call of SetSecurityGroupsWithContext.
func (mr *MockELBV2MockRecorder) SetSecurityGroupsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSecurityGroupsWithContext", reflect.TypeOf((*MockELBV2)(nil).SetSecurityGroupsWithContext), varargs...)
}

// SetSubnets mocks base method.
func (m *MockELBV2) SetSubnets(arg0 *elbv2.SetSubnetsInput) (*elbv2.SetSubnetsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSubnets", arg0)
	ret0, _ := ret[0].(*elbv2.SetSubnetsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSubnets indicates an expected call of SetSubnets.
func (mr *MockELBV2MockRecorder) SetSubnets(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSubnets", reflect.TypeOf((*MockELBV2)(nil).SetSubnets), arg0)
}

// SetSubnetsRequest mocks base method.
func (m *MockELBV2) SetSubnetsRequest(arg0 *elbv2.SetSubnetsInput) (*request.Request, *elbv2.SetSubnetsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSubnetsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*elbv2.SetSubnetsOutput)
	return ret0, ret1
}

// SetSubnetsRequest indicates an expected call of SetSubnetsRequest.
func (mr *MockELBV2MockRecorder) SetSubnetsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSubnetsRequest", reflect.TypeOf((*MockELBV2)(nil).SetSubnetsRequest), arg0)
}

// SetSubnetsWithContext mocks base method.
func (m *MockELBV2) SetSubnetsWithContext(arg0 context.Context, arg1 *elbv2.SetSubnetsInput, arg2 ...request.Option) (*elbv2.SetSubnetsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSubnetsWithContext", varargs...)
	ret0, _ := ret[0].(*elbv2.SetSubnetsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSubnetsWithContext indicates an expected call of SetSubnetsWithContext.
func (mr *MockELBV2MockRecorder) SetSubnetsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSubnetsWithContext", reflect.TypeOf((*MockELBV2)(nil).SetSubnetsWithContext), varargs...)
}

// WaitUntilLoadBalancerAvailable mocks base method.
func (m *MockELBV2) WaitUntilLoadBalancerAvailable(arg0 *elbv2.DescribeLoadBalancersInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitUntilLoadBalancerAvailable", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilLoadBalancerAvailable indicates an expected call of WaitUntilLoadBalancerAvailable.
func (mr *MockELBV2MockRecorder) WaitUntilLoadBalancerAvailable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilLoadBalancerAvailable", reflect.TypeOf((*MockELBV2)(nil).WaitUntilLoadBalancerAvailable), arg0)
}

// WaitUntilLoadBalancerAvailableWithContext mocks base method.
func (m *MockELBV2) WaitUntilLoadBalancerAvailableWithContext(arg0 context.Context, arg1 *elbv2.DescribeLoadBalancersInput, arg2 ...request.WaiterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WaitUntilLoadBalancerAvailableWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilLoadBalancerAvailableWithContext indicates an expected call of WaitUntilLoadBalancerAvailableWithContext.
func (mr *MockELBV2MockRecorder) WaitUntilLoadBalancerAvailableWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilLoadBalancerAvailableWithContext", reflect.TypeOf((*MockELBV2)(nil).WaitUntilLoadBalancerAvailableWithContext), varargs...)
}

// WaitUntilLoadBalancerExists mocks base method.
func (m *MockELBV2) WaitUntilLoadBalancerExists(arg0 *elbv2.DescribeLoadBalancersInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitUntilLoadBalancerExists", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilLoadBalancerExists indicates an expected call of WaitUntilLoadBalancerExists.
func (mr *MockELBV2MockRecorder) WaitUntilLoadBalancerExists(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilLoadBalancerExists", reflect.TypeOf((*MockELBV2)(nil).WaitUntilLoadBalancerExists), arg0)
}

// WaitUntilLoadBalancerExistsWithContext mocks base method.
func (m *MockELBV2) WaitUntilLoadBalancerExistsWithContext(arg0 context.Context, arg1 *elbv2.DescribeLoadBalancersInput, arg2 ...request.WaiterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WaitUntilLoadBalancerExistsWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilLoadBalancerExistsWithContext indicates an expected call of WaitUntilLoadBalancerExistsWithContext.
func (mr *MockELBV2MockRecorder) WaitUntilLoadBalancerExistsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilLoadBalancerExistsWithContext", reflect.TypeOf((*MockELBV2)(nil).WaitUntilLoadBalancerExistsWithContext), varargs...)
}

// WaitUntilLoadBalancersDeleted mocks base method.
func (m *MockELBV2) WaitUntilLoadBalancersDeleted(arg0 *elbv2.DescribeLoadBalancersInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitUntilLoadBalancersDeleted", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilLoadBalancersDeleted indicates an expected call of WaitUntilLoadBalancersDeleted.
func (mr *MockELBV2MockRecorder) WaitUntilLoadBalancersDeleted(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilLoadBalancersDeleted", reflect.TypeOf((*MockELBV2)(nil).WaitUntilLoadBalancersDeleted), arg0)
}

// WaitUntilLoadBalancersDeletedWithContext mocks base method.
func (m *MockELBV2) WaitUntilLoadBalancersDeletedWithContext(arg0 context.Context, arg1 *elbv2.DescribeLoadBalancersInput, arg2 ...request.WaiterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WaitUntilLoadBalancersDeletedWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilLoadBalancersDeletedWithContext indicates an expected call of WaitUntilLoadBalancersDeletedWithContext.
func (mr *MockELBV2MockRecorder) WaitUntilLoadBalancersDeletedWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilLoadBalancersDeletedWithContext", reflect.TypeOf((*MockELBV2)(nil).WaitUntilLoadBalancersDeletedWithContext), varargs...)
}

// WaitUntilTargetDeregistered mocks base method.
func (m *MockELBV2) WaitUntilTargetDeregistered(arg0 *elbv2.DescribeTargetHealthInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitUntilTargetDeregistered", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilTargetDeregistered indicates an expected call of WaitUntilTargetDeregistered.
func (mr *MockELBV2MockRecorder) WaitUntilTargetDeregistered(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilTargetDeregistered", reflect.TypeOf((*MockELBV2)(nil).WaitUntilTargetDeregistered), arg0)
}

// WaitUntilTargetDeregisteredWithContext mocks base method.
func (m *MockELBV2) WaitUntilTargetDeregisteredWithContext(arg0 context.Context, arg1 *elbv2.DescribeTargetHealthInput, arg2 ...request.WaiterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WaitUntilTargetDeregisteredWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilTargetDeregisteredWithContext indicates an expected call of WaitUntilTargetDeregisteredWithContext.
func (mr *MockELBV2MockRecorder) WaitUntilTargetDeregisteredWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilTargetDeregisteredWithContext", reflect.TypeOf((*MockELBV2)(nil).WaitUntilTargetDeregisteredWithContext), varargs...)
}

// WaitUntilTargetInService mocks base method.
func (m *MockELBV2) WaitUntilTargetInService(arg0 *elbv2.DescribeTargetHealthInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitUntilTargetInService", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilTargetInService indicates an expected call of WaitUntilTargetInService.
func (mr *MockELBV2MockRecorder) WaitUntilTargetInService(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilTargetInService", reflect.TypeOf((*MockELBV2)(nil).WaitUntilTargetInService), arg0)
}

// WaitUntilTargetInServiceWithContext mocks base method.
func (m *MockELBV2) WaitUntilTargetInServiceWithContext(arg0 context.Context, arg1 *elbv2.DescribeTargetHealthInput, arg2 ...request.WaiterOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WaitUntilTargetInServiceWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilTargetInServiceWithContext indicates an expected call of WaitUntilTargetInServiceWithContext.
func (mr *MockELBV2MockRecorder) WaitUntilTargetInServiceWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilTargetInServiceWithContext", reflect.TypeOf((*MockELBV2)(nil).WaitUntilTargetInServiceWithContext), varargs...)
}
