// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/aws/services (interfaces: Shield)

// Package services is a generated GoMock package.
package services

import (
	context "context"
	reflect "reflect"

	request "github.com/aws/aws-sdk-go/aws/request"
	shield "github.com/aws/aws-sdk-go/service/shield"
	gomock "github.com/golang/mock/gomock"
)

// MockShield is a mock of Shield interface.
type MockShield struct {
	ctrl     *gomock.Controller
	recorder *MockShieldMockRecorder
}

// MockShieldMockRecorder is the mock recorder for MockShield.
type MockShieldMockRecorder struct {
	mock *MockShield
}

// NewMockShield creates a new mock instance.
func NewMockShield(ctrl *gomock.Controller) *MockShield {
	mock := &MockShield{ctrl: ctrl}
	mock.recorder = &MockShieldMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockShield) EXPECT() *MockShieldMockRecorder {
	return m.recorder
}

// AssociateDRTLogBucket mocks base method.
func (m *MockShield) AssociateDRTLogBucket(arg0 *shield.AssociateDRTLogBucketInput) (*shield.AssociateDRTLogBucketOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateDRTLogBucket", arg0)
	ret0, _ := ret[0].(*shield.AssociateDRTLogBucketOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateDRTLogBucket indicates an expected call of AssociateDRTLogBucket.
func (mr *MockShieldMockRecorder) AssociateDRTLogBucket(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateDRTLogBucket", reflect.TypeOf((*MockShield)(nil).AssociateDRTLogBucket), arg0)
}

// AssociateDRTLogBucketRequest mocks base method.
func (m *MockShield) AssociateDRTLogBucketRequest(arg0 *shield.AssociateDRTLogBucketInput) (*request.Request, *shield.AssociateDRTLogBucketOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateDRTLogBucketRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.AssociateDRTLogBucketOutput)
	return ret0, ret1
}

// AssociateDRTLogBucketRequest indicates an expected call of AssociateDRTLogBucketRequest.
func (mr *MockShieldMockRecorder) AssociateDRTLogBucketRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateDRTLogBucketRequest", reflect.TypeOf((*MockShield)(nil).AssociateDRTLogBucketRequest), arg0)
}

// AssociateDRTLogBucketWithContext mocks base method.
func (m *MockShield) AssociateDRTLogBucketWithContext(arg0 context.Context, arg1 *shield.AssociateDRTLogBucketInput, arg2 ...request.Option) (*shield.AssociateDRTLogBucketOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AssociateDRTLogBucketWithContext", varargs...)
	ret0, _ := ret[0].(*shield.AssociateDRTLogBucketOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateDRTLogBucketWithContext indicates an expected call of AssociateDRTLogBucketWithContext.
func (mr *MockShieldMockRecorder) AssociateDRTLogBucketWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateDRTLogBucketWithContext", reflect.TypeOf((*MockShield)(nil).AssociateDRTLogBucketWithContext), varargs...)
}

// AssociateDRTRole mocks base method.
func (m *MockShield) AssociateDRTRole(arg0 *shield.AssociateDRTRoleInput) (*shield.AssociateDRTRoleOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateDRTRole", arg0)
	ret0, _ := ret[0].(*shield.AssociateDRTRoleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateDRTRole indicates an expected call of AssociateDRTRole.
func (mr *MockShieldMockRecorder) AssociateDRTRole(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateDRTRole", reflect.TypeOf((*MockShield)(nil).AssociateDRTRole), arg0)
}

// AssociateDRTRoleRequest mocks base method.
func (m *MockShield) AssociateDRTRoleRequest(arg0 *shield.AssociateDRTRoleInput) (*request.Request, *shield.AssociateDRTRoleOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateDRTRoleRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.AssociateDRTRoleOutput)
	return ret0, ret1
}

// AssociateDRTRoleRequest indicates an expected call of AssociateDRTRoleRequest.
func (mr *MockShieldMockRecorder) AssociateDRTRoleRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateDRTRoleRequest", reflect.TypeOf((*MockShield)(nil).AssociateDRTRoleRequest), arg0)
}

// AssociateDRTRoleWithContext mocks base method.
func (m *MockShield) AssociateDRTRoleWithContext(arg0 context.Context, arg1 *shield.AssociateDRTRoleInput, arg2 ...request.Option) (*shield.AssociateDRTRoleOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AssociateDRTRoleWithContext", varargs...)
	ret0, _ := ret[0].(*shield.AssociateDRTRoleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateDRTRoleWithContext indicates an expected call of AssociateDRTRoleWithContext.
func (mr *MockShieldMockRecorder) AssociateDRTRoleWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateDRTRoleWithContext", reflect.TypeOf((*MockShield)(nil).AssociateDRTRoleWithContext), varargs...)
}

// AssociateHealthCheck mocks base method.
func (m *MockShield) AssociateHealthCheck(arg0 *shield.AssociateHealthCheckInput) (*shield.AssociateHealthCheckOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateHealthCheck", arg0)
	ret0, _ := ret[0].(*shield.AssociateHealthCheckOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateHealthCheck indicates an expected call of AssociateHealthCheck.
func (mr *MockShieldMockRecorder) AssociateHealthCheck(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateHealthCheck", reflect.TypeOf((*MockShield)(nil).AssociateHealthCheck), arg0)
}

// AssociateHealthCheckRequest mocks base method.
func (m *MockShield) AssociateHealthCheckRequest(arg0 *shield.AssociateHealthCheckInput) (*request.Request, *shield.AssociateHealthCheckOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateHealthCheckRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.AssociateHealthCheckOutput)
	return ret0, ret1
}

// AssociateHealthCheckRequest indicates an expected call of AssociateHealthCheckRequest.
func (mr *MockShieldMockRecorder) AssociateHealthCheckRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateHealthCheckRequest", reflect.TypeOf((*MockShield)(nil).AssociateHealthCheckRequest), arg0)
}

// AssociateHealthCheckWithContext mocks base method.
func (m *MockShield) AssociateHealthCheckWithContext(arg0 context.Context, arg1 *shield.AssociateHealthCheckInput, arg2 ...request.Option) (*shield.AssociateHealthCheckOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AssociateHealthCheckWithContext", varargs...)
	ret0, _ := ret[0].(*shield.AssociateHealthCheckOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateHealthCheckWithContext indicates an expected call of AssociateHealthCheckWithContext.
func (mr *MockShieldMockRecorder) AssociateHealthCheckWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateHealthCheckWithContext", reflect.TypeOf((*MockShield)(nil).AssociateHealthCheckWithContext), varargs...)
}

// AssociateProactiveEngagementDetails mocks base method.
func (m *MockShield) AssociateProactiveEngagementDetails(arg0 *shield.AssociateProactiveEngagementDetailsInput) (*shield.AssociateProactiveEngagementDetailsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateProactiveEngagementDetails", arg0)
	ret0, _ := ret[0].(*shield.AssociateProactiveEngagementDetailsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateProactiveEngagementDetails indicates an expected call of AssociateProactiveEngagementDetails.
func (mr *MockShieldMockRecorder) AssociateProactiveEngagementDetails(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateProactiveEngagementDetails", reflect.TypeOf((*MockShield)(nil).AssociateProactiveEngagementDetails), arg0)
}

// AssociateProactiveEngagementDetailsRequest mocks base method.
func (m *MockShield) AssociateProactiveEngagementDetailsRequest(arg0 *shield.AssociateProactiveEngagementDetailsInput) (*request.Request, *shield.AssociateProactiveEngagementDetailsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssociateProactiveEngagementDetailsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.AssociateProactiveEngagementDetailsOutput)
	return ret0, ret1
}

// AssociateProactiveEngagementDetailsRequest indicates an expected call of AssociateProactiveEngagementDetailsRequest.
func (mr *MockShieldMockRecorder) AssociateProactiveEngagementDetailsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateProactiveEngagementDetailsRequest", reflect.TypeOf((*MockShield)(nil).AssociateProactiveEngagementDetailsRequest), arg0)
}

// AssociateProactiveEngagementDetailsWithContext mocks base method.
func (m *MockShield) AssociateProactiveEngagementDetailsWithContext(arg0 context.Context, arg1 *shield.AssociateProactiveEngagementDetailsInput, arg2 ...request.Option) (*shield.AssociateProactiveEngagementDetailsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AssociateProactiveEngagementDetailsWithContext", varargs...)
	ret0, _ := ret[0].(*shield.AssociateProactiveEngagementDetailsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssociateProactiveEngagementDetailsWithContext indicates an expected call of AssociateProactiveEngagementDetailsWithContext.
func (mr *MockShieldMockRecorder) AssociateProactiveEngagementDetailsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssociateProactiveEngagementDetailsWithContext", reflect.TypeOf((*MockShield)(nil).AssociateProactiveEngagementDetailsWithContext), varargs...)
}

// CreateProtection mocks base method.
func (m *MockShield) CreateProtection(arg0 *shield.CreateProtectionInput) (*shield.CreateProtectionOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateProtection", arg0)
	ret0, _ := ret[0].(*shield.CreateProtectionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateProtection indicates an expected call of CreateProtection.
func (mr *MockShieldMockRecorder) CreateProtection(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProtection", reflect.TypeOf((*MockShield)(nil).CreateProtection), arg0)
}

// CreateProtectionGroup mocks base method.
func (m *MockShield) CreateProtectionGroup(arg0 *shield.CreateProtectionGroupInput) (*shield.CreateProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateProtectionGroup", arg0)
	ret0, _ := ret[0].(*shield.CreateProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateProtectionGroup indicates an expected call of CreateProtectionGroup.
func (mr *MockShieldMockRecorder) CreateProtectionGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProtectionGroup", reflect.TypeOf((*MockShield)(nil).CreateProtectionGroup), arg0)
}

// CreateProtectionGroupRequest mocks base method.
func (m *MockShield) CreateProtectionGroupRequest(arg0 *shield.CreateProtectionGroupInput) (*request.Request, *shield.CreateProtectionGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateProtectionGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.CreateProtectionGroupOutput)
	return ret0, ret1
}

// CreateProtectionGroupRequest indicates an expected call of CreateProtectionGroupRequest.
func (mr *MockShieldMockRecorder) CreateProtectionGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProtectionGroupRequest", reflect.TypeOf((*MockShield)(nil).CreateProtectionGroupRequest), arg0)
}

// CreateProtectionGroupWithContext mocks base method.
func (m *MockShield) CreateProtectionGroupWithContext(arg0 context.Context, arg1 *shield.CreateProtectionGroupInput, arg2 ...request.Option) (*shield.CreateProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateProtectionGroupWithContext", varargs...)
	ret0, _ := ret[0].(*shield.CreateProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateProtectionGroupWithContext indicates an expected call of CreateProtectionGroupWithContext.
func (mr *MockShieldMockRecorder) CreateProtectionGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProtectionGroupWithContext", reflect.TypeOf((*MockShield)(nil).CreateProtectionGroupWithContext), varargs...)
}

// CreateProtectionRequest mocks base method.
func (m *MockShield) CreateProtectionRequest(arg0 *shield.CreateProtectionInput) (*request.Request, *shield.CreateProtectionOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateProtectionRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.CreateProtectionOutput)
	return ret0, ret1
}

// CreateProtectionRequest indicates an expected call of CreateProtectionRequest.
func (mr *MockShieldMockRecorder) CreateProtectionRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProtectionRequest", reflect.TypeOf((*MockShield)(nil).CreateProtectionRequest), arg0)
}

// CreateProtectionWithContext mocks base method.
func (m *MockShield) CreateProtectionWithContext(arg0 context.Context, arg1 *shield.CreateProtectionInput, arg2 ...request.Option) (*shield.CreateProtectionOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateProtectionWithContext", varargs...)
	ret0, _ := ret[0].(*shield.CreateProtectionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateProtectionWithContext indicates an expected call of CreateProtectionWithContext.
func (mr *MockShieldMockRecorder) CreateProtectionWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProtectionWithContext", reflect.TypeOf((*MockShield)(nil).CreateProtectionWithContext), varargs...)
}

// CreateSubscription mocks base method.
func (m *MockShield) CreateSubscription(arg0 *shield.CreateSubscriptionInput) (*shield.CreateSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubscription", arg0)
	ret0, _ := ret[0].(*shield.CreateSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubscription indicates an expected call of CreateSubscription.
func (mr *MockShieldMockRecorder) CreateSubscription(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubscription", reflect.TypeOf((*MockShield)(nil).CreateSubscription), arg0)
}

// CreateSubscriptionRequest mocks base method.
func (m *MockShield) CreateSubscriptionRequest(arg0 *shield.CreateSubscriptionInput) (*request.Request, *shield.CreateSubscriptionOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubscriptionRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.CreateSubscriptionOutput)
	return ret0, ret1
}

// CreateSubscriptionRequest indicates an expected call of CreateSubscriptionRequest.
func (mr *MockShieldMockRecorder) CreateSubscriptionRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubscriptionRequest", reflect.TypeOf((*MockShield)(nil).CreateSubscriptionRequest), arg0)
}

// CreateSubscriptionWithContext mocks base method.
func (m *MockShield) CreateSubscriptionWithContext(arg0 context.Context, arg1 *shield.CreateSubscriptionInput, arg2 ...request.Option) (*shield.CreateSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSubscriptionWithContext", varargs...)
	ret0, _ := ret[0].(*shield.CreateSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubscriptionWithContext indicates an expected call of CreateSubscriptionWithContext.
func (mr *MockShieldMockRecorder) CreateSubscriptionWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubscriptionWithContext", reflect.TypeOf((*MockShield)(nil).CreateSubscriptionWithContext), varargs...)
}

// DeleteProtection mocks base method.
func (m *MockShield) DeleteProtection(arg0 *shield.DeleteProtectionInput) (*shield.DeleteProtectionOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteProtection", arg0)
	ret0, _ := ret[0].(*shield.DeleteProtectionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteProtection indicates an expected call of DeleteProtection.
func (mr *MockShieldMockRecorder) DeleteProtection(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProtection", reflect.TypeOf((*MockShield)(nil).DeleteProtection), arg0)
}

// DeleteProtectionGroup mocks base method.
func (m *MockShield) DeleteProtectionGroup(arg0 *shield.DeleteProtectionGroupInput) (*shield.DeleteProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteProtectionGroup", arg0)
	ret0, _ := ret[0].(*shield.DeleteProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteProtectionGroup indicates an expected call of DeleteProtectionGroup.
func (mr *MockShieldMockRecorder) DeleteProtectionGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProtectionGroup", reflect.TypeOf((*MockShield)(nil).DeleteProtectionGroup), arg0)
}

// DeleteProtectionGroupRequest mocks base method.
func (m *MockShield) DeleteProtectionGroupRequest(arg0 *shield.DeleteProtectionGroupInput) (*request.Request, *shield.DeleteProtectionGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteProtectionGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DeleteProtectionGroupOutput)
	return ret0, ret1
}

// DeleteProtectionGroupRequest indicates an expected call of DeleteProtectionGroupRequest.
func (mr *MockShieldMockRecorder) DeleteProtectionGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProtectionGroupRequest", reflect.TypeOf((*MockShield)(nil).DeleteProtectionGroupRequest), arg0)
}

// DeleteProtectionGroupWithContext mocks base method.
func (m *MockShield) DeleteProtectionGroupWithContext(arg0 context.Context, arg1 *shield.DeleteProtectionGroupInput, arg2 ...request.Option) (*shield.DeleteProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteProtectionGroupWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DeleteProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteProtectionGroupWithContext indicates an expected call of DeleteProtectionGroupWithContext.
func (mr *MockShieldMockRecorder) DeleteProtectionGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProtectionGroupWithContext", reflect.TypeOf((*MockShield)(nil).DeleteProtectionGroupWithContext), varargs...)
}

// DeleteProtectionRequest mocks base method.
func (m *MockShield) DeleteProtectionRequest(arg0 *shield.DeleteProtectionInput) (*request.Request, *shield.DeleteProtectionOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteProtectionRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DeleteProtectionOutput)
	return ret0, ret1
}

// DeleteProtectionRequest indicates an expected call of DeleteProtectionRequest.
func (mr *MockShieldMockRecorder) DeleteProtectionRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProtectionRequest", reflect.TypeOf((*MockShield)(nil).DeleteProtectionRequest), arg0)
}

// DeleteProtectionWithContext mocks base method.
func (m *MockShield) DeleteProtectionWithContext(arg0 context.Context, arg1 *shield.DeleteProtectionInput, arg2 ...request.Option) (*shield.DeleteProtectionOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteProtectionWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DeleteProtectionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteProtectionWithContext indicates an expected call of DeleteProtectionWithContext.
func (mr *MockShieldMockRecorder) DeleteProtectionWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteProtectionWithContext", reflect.TypeOf((*MockShield)(nil).DeleteProtectionWithContext), varargs...)
}

// DeleteSubscription mocks base method.
func (m *MockShield) DeleteSubscription(arg0 *shield.DeleteSubscriptionInput) (*shield.DeleteSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSubscription", arg0)
	ret0, _ := ret[0].(*shield.DeleteSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSubscription indicates an expected call of DeleteSubscription.
func (mr *MockShieldMockRecorder) DeleteSubscription(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSubscription", reflect.TypeOf((*MockShield)(nil).DeleteSubscription), arg0)
}

// DeleteSubscriptionRequest mocks base method.
func (m *MockShield) DeleteSubscriptionRequest(arg0 *shield.DeleteSubscriptionInput) (*request.Request, *shield.DeleteSubscriptionOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSubscriptionRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DeleteSubscriptionOutput)
	return ret0, ret1
}

// DeleteSubscriptionRequest indicates an expected call of DeleteSubscriptionRequest.
func (mr *MockShieldMockRecorder) DeleteSubscriptionRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSubscriptionRequest", reflect.TypeOf((*MockShield)(nil).DeleteSubscriptionRequest), arg0)
}

// DeleteSubscriptionWithContext mocks base method.
func (m *MockShield) DeleteSubscriptionWithContext(arg0 context.Context, arg1 *shield.DeleteSubscriptionInput, arg2 ...request.Option) (*shield.DeleteSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSubscriptionWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DeleteSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSubscriptionWithContext indicates an expected call of DeleteSubscriptionWithContext.
func (mr *MockShieldMockRecorder) DeleteSubscriptionWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSubscriptionWithContext", reflect.TypeOf((*MockShield)(nil).DeleteSubscriptionWithContext), varargs...)
}

// DescribeAttack mocks base method.
func (m *MockShield) DescribeAttack(arg0 *shield.DescribeAttackInput) (*shield.DescribeAttackOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAttack", arg0)
	ret0, _ := ret[0].(*shield.DescribeAttackOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAttack indicates an expected call of DescribeAttack.
func (mr *MockShieldMockRecorder) DescribeAttack(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAttack", reflect.TypeOf((*MockShield)(nil).DescribeAttack), arg0)
}

// DescribeAttackRequest mocks base method.
func (m *MockShield) DescribeAttackRequest(arg0 *shield.DescribeAttackInput) (*request.Request, *shield.DescribeAttackOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAttackRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DescribeAttackOutput)
	return ret0, ret1
}

// DescribeAttackRequest indicates an expected call of DescribeAttackRequest.
func (mr *MockShieldMockRecorder) DescribeAttackRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAttackRequest", reflect.TypeOf((*MockShield)(nil).DescribeAttackRequest), arg0)
}

// DescribeAttackStatistics mocks base method.
func (m *MockShield) DescribeAttackStatistics(arg0 *shield.DescribeAttackStatisticsInput) (*shield.DescribeAttackStatisticsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAttackStatistics", arg0)
	ret0, _ := ret[0].(*shield.DescribeAttackStatisticsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAttackStatistics indicates an expected call of DescribeAttackStatistics.
func (mr *MockShieldMockRecorder) DescribeAttackStatistics(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAttackStatistics", reflect.TypeOf((*MockShield)(nil).DescribeAttackStatistics), arg0)
}

// DescribeAttackStatisticsRequest mocks base method.
func (m *MockShield) DescribeAttackStatisticsRequest(arg0 *shield.DescribeAttackStatisticsInput) (*request.Request, *shield.DescribeAttackStatisticsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAttackStatisticsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DescribeAttackStatisticsOutput)
	return ret0, ret1
}

// DescribeAttackStatisticsRequest indicates an expected call of DescribeAttackStatisticsRequest.
func (mr *MockShieldMockRecorder) DescribeAttackStatisticsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAttackStatisticsRequest", reflect.TypeOf((*MockShield)(nil).DescribeAttackStatisticsRequest), arg0)
}

// DescribeAttackStatisticsWithContext mocks base method.
func (m *MockShield) DescribeAttackStatisticsWithContext(arg0 context.Context, arg1 *shield.DescribeAttackStatisticsInput, arg2 ...request.Option) (*shield.DescribeAttackStatisticsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeAttackStatisticsWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DescribeAttackStatisticsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAttackStatisticsWithContext indicates an expected call of DescribeAttackStatisticsWithContext.
func (mr *MockShieldMockRecorder) DescribeAttackStatisticsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAttackStatisticsWithContext", reflect.TypeOf((*MockShield)(nil).DescribeAttackStatisticsWithContext), varargs...)
}

// DescribeAttackWithContext mocks base method.
func (m *MockShield) DescribeAttackWithContext(arg0 context.Context, arg1 *shield.DescribeAttackInput, arg2 ...request.Option) (*shield.DescribeAttackOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeAttackWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DescribeAttackOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAttackWithContext indicates an expected call of DescribeAttackWithContext.
func (mr *MockShieldMockRecorder) DescribeAttackWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAttackWithContext", reflect.TypeOf((*MockShield)(nil).DescribeAttackWithContext), varargs...)
}

// DescribeDRTAccess mocks base method.
func (m *MockShield) DescribeDRTAccess(arg0 *shield.DescribeDRTAccessInput) (*shield.DescribeDRTAccessOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDRTAccess", arg0)
	ret0, _ := ret[0].(*shield.DescribeDRTAccessOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDRTAccess indicates an expected call of DescribeDRTAccess.
func (mr *MockShieldMockRecorder) DescribeDRTAccess(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDRTAccess", reflect.TypeOf((*MockShield)(nil).DescribeDRTAccess), arg0)
}

// DescribeDRTAccessRequest mocks base method.
func (m *MockShield) DescribeDRTAccessRequest(arg0 *shield.DescribeDRTAccessInput) (*request.Request, *shield.DescribeDRTAccessOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDRTAccessRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DescribeDRTAccessOutput)
	return ret0, ret1
}

// DescribeDRTAccessRequest indicates an expected call of DescribeDRTAccessRequest.
func (mr *MockShieldMockRecorder) DescribeDRTAccessRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDRTAccessRequest", reflect.TypeOf((*MockShield)(nil).DescribeDRTAccessRequest), arg0)
}

// DescribeDRTAccessWithContext mocks base method.
func (m *MockShield) DescribeDRTAccessWithContext(arg0 context.Context, arg1 *shield.DescribeDRTAccessInput, arg2 ...request.Option) (*shield.DescribeDRTAccessOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeDRTAccessWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DescribeDRTAccessOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDRTAccessWithContext indicates an expected call of DescribeDRTAccessWithContext.
func (mr *MockShieldMockRecorder) DescribeDRTAccessWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDRTAccessWithContext", reflect.TypeOf((*MockShield)(nil).DescribeDRTAccessWithContext), varargs...)
}

// DescribeEmergencyContactSettings mocks base method.
func (m *MockShield) DescribeEmergencyContactSettings(arg0 *shield.DescribeEmergencyContactSettingsInput) (*shield.DescribeEmergencyContactSettingsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeEmergencyContactSettings", arg0)
	ret0, _ := ret[0].(*shield.DescribeEmergencyContactSettingsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeEmergencyContactSettings indicates an expected call of DescribeEmergencyContactSettings.
func (mr *MockShieldMockRecorder) DescribeEmergencyContactSettings(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeEmergencyContactSettings", reflect.TypeOf((*MockShield)(nil).DescribeEmergencyContactSettings), arg0)
}

// DescribeEmergencyContactSettingsRequest mocks base method.
func (m *MockShield) DescribeEmergencyContactSettingsRequest(arg0 *shield.DescribeEmergencyContactSettingsInput) (*request.Request, *shield.DescribeEmergencyContactSettingsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeEmergencyContactSettingsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DescribeEmergencyContactSettingsOutput)
	return ret0, ret1
}

// DescribeEmergencyContactSettingsRequest indicates an expected call of DescribeEmergencyContactSettingsRequest.
func (mr *MockShieldMockRecorder) DescribeEmergencyContactSettingsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeEmergencyContactSettingsRequest", reflect.TypeOf((*MockShield)(nil).DescribeEmergencyContactSettingsRequest), arg0)
}

// DescribeEmergencyContactSettingsWithContext mocks base method.
func (m *MockShield) DescribeEmergencyContactSettingsWithContext(arg0 context.Context, arg1 *shield.DescribeEmergencyContactSettingsInput, arg2 ...request.Option) (*shield.DescribeEmergencyContactSettingsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeEmergencyContactSettingsWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DescribeEmergencyContactSettingsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeEmergencyContactSettingsWithContext indicates an expected call of DescribeEmergencyContactSettingsWithContext.
func (mr *MockShieldMockRecorder) DescribeEmergencyContactSettingsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeEmergencyContactSettingsWithContext", reflect.TypeOf((*MockShield)(nil).DescribeEmergencyContactSettingsWithContext), varargs...)
}

// DescribeProtection mocks base method.
func (m *MockShield) DescribeProtection(arg0 *shield.DescribeProtectionInput) (*shield.DescribeProtectionOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeProtection", arg0)
	ret0, _ := ret[0].(*shield.DescribeProtectionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeProtection indicates an expected call of DescribeProtection.
func (mr *MockShieldMockRecorder) DescribeProtection(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeProtection", reflect.TypeOf((*MockShield)(nil).DescribeProtection), arg0)
}

// DescribeProtectionGroup mocks base method.
func (m *MockShield) DescribeProtectionGroup(arg0 *shield.DescribeProtectionGroupInput) (*shield.DescribeProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeProtectionGroup", arg0)
	ret0, _ := ret[0].(*shield.DescribeProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeProtectionGroup indicates an expected call of DescribeProtectionGroup.
func (mr *MockShieldMockRecorder) DescribeProtectionGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeProtectionGroup", reflect.TypeOf((*MockShield)(nil).DescribeProtectionGroup), arg0)
}

// DescribeProtectionGroupRequest mocks base method.
func (m *MockShield) DescribeProtectionGroupRequest(arg0 *shield.DescribeProtectionGroupInput) (*request.Request, *shield.DescribeProtectionGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeProtectionGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DescribeProtectionGroupOutput)
	return ret0, ret1
}

// DescribeProtectionGroupRequest indicates an expected call of DescribeProtectionGroupRequest.
func (mr *MockShieldMockRecorder) DescribeProtectionGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeProtectionGroupRequest", reflect.TypeOf((*MockShield)(nil).DescribeProtectionGroupRequest), arg0)
}

// DescribeProtectionGroupWithContext mocks base method.
func (m *MockShield) DescribeProtectionGroupWithContext(arg0 context.Context, arg1 *shield.DescribeProtectionGroupInput, arg2 ...request.Option) (*shield.DescribeProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeProtectionGroupWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DescribeProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeProtectionGroupWithContext indicates an expected call of DescribeProtectionGroupWithContext.
func (mr *MockShieldMockRecorder) DescribeProtectionGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeProtectionGroupWithContext", reflect.TypeOf((*MockShield)(nil).DescribeProtectionGroupWithContext), varargs...)
}

// DescribeProtectionRequest mocks base method.
func (m *MockShield) DescribeProtectionRequest(arg0 *shield.DescribeProtectionInput) (*request.Request, *shield.DescribeProtectionOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeProtectionRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DescribeProtectionOutput)
	return ret0, ret1
}

// DescribeProtectionRequest indicates an expected call of DescribeProtectionRequest.
func (mr *MockShieldMockRecorder) DescribeProtectionRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeProtectionRequest", reflect.TypeOf((*MockShield)(nil).DescribeProtectionRequest), arg0)
}

// DescribeProtectionWithContext mocks base method.
func (m *MockShield) DescribeProtectionWithContext(arg0 context.Context, arg1 *shield.DescribeProtectionInput, arg2 ...request.Option) (*shield.DescribeProtectionOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeProtectionWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DescribeProtectionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeProtectionWithContext indicates an expected call of DescribeProtectionWithContext.
func (mr *MockShieldMockRecorder) DescribeProtectionWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeProtectionWithContext", reflect.TypeOf((*MockShield)(nil).DescribeProtectionWithContext), varargs...)
}

// DescribeSubscription mocks base method.
func (m *MockShield) DescribeSubscription(arg0 *shield.DescribeSubscriptionInput) (*shield.DescribeSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSubscription", arg0)
	ret0, _ := ret[0].(*shield.DescribeSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSubscription indicates an expected call of DescribeSubscription.
func (mr *MockShieldMockRecorder) DescribeSubscription(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSubscription", reflect.TypeOf((*MockShield)(nil).DescribeSubscription), arg0)
}

// DescribeSubscriptionRequest mocks base method.
func (m *MockShield) DescribeSubscriptionRequest(arg0 *shield.DescribeSubscriptionInput) (*request.Request, *shield.DescribeSubscriptionOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSubscriptionRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DescribeSubscriptionOutput)
	return ret0, ret1
}

// DescribeSubscriptionRequest indicates an expected call of DescribeSubscriptionRequest.
func (mr *MockShieldMockRecorder) DescribeSubscriptionRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSubscriptionRequest", reflect.TypeOf((*MockShield)(nil).DescribeSubscriptionRequest), arg0)
}

// DescribeSubscriptionWithContext mocks base method.
func (m *MockShield) DescribeSubscriptionWithContext(arg0 context.Context, arg1 *shield.DescribeSubscriptionInput, arg2 ...request.Option) (*shield.DescribeSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeSubscriptionWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DescribeSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSubscriptionWithContext indicates an expected call of DescribeSubscriptionWithContext.
func (mr *MockShieldMockRecorder) DescribeSubscriptionWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSubscriptionWithContext", reflect.TypeOf((*MockShield)(nil).DescribeSubscriptionWithContext), varargs...)
}

// DisableApplicationLayerAutomaticResponse mocks base method.
func (m *MockShield) DisableApplicationLayerAutomaticResponse(arg0 *shield.DisableApplicationLayerAutomaticResponseInput) (*shield.DisableApplicationLayerAutomaticResponseOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableApplicationLayerAutomaticResponse", arg0)
	ret0, _ := ret[0].(*shield.DisableApplicationLayerAutomaticResponseOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableApplicationLayerAutomaticResponse indicates an expected call of DisableApplicationLayerAutomaticResponse.
func (mr *MockShieldMockRecorder) DisableApplicationLayerAutomaticResponse(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableApplicationLayerAutomaticResponse", reflect.TypeOf((*MockShield)(nil).DisableApplicationLayerAutomaticResponse), arg0)
}

// DisableApplicationLayerAutomaticResponseRequest mocks base method.
func (m *MockShield) DisableApplicationLayerAutomaticResponseRequest(arg0 *shield.DisableApplicationLayerAutomaticResponseInput) (*request.Request, *shield.DisableApplicationLayerAutomaticResponseOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableApplicationLayerAutomaticResponseRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DisableApplicationLayerAutomaticResponseOutput)
	return ret0, ret1
}

// DisableApplicationLayerAutomaticResponseRequest indicates an expected call of DisableApplicationLayerAutomaticResponseRequest.
func (mr *MockShieldMockRecorder) DisableApplicationLayerAutomaticResponseRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableApplicationLayerAutomaticResponseRequest", reflect.TypeOf((*MockShield)(nil).DisableApplicationLayerAutomaticResponseRequest), arg0)
}

// DisableApplicationLayerAutomaticResponseWithContext mocks base method.
func (m *MockShield) DisableApplicationLayerAutomaticResponseWithContext(arg0 context.Context, arg1 *shield.DisableApplicationLayerAutomaticResponseInput, arg2 ...request.Option) (*shield.DisableApplicationLayerAutomaticResponseOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisableApplicationLayerAutomaticResponseWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DisableApplicationLayerAutomaticResponseOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableApplicationLayerAutomaticResponseWithContext indicates an expected call of DisableApplicationLayerAutomaticResponseWithContext.
func (mr *MockShieldMockRecorder) DisableApplicationLayerAutomaticResponseWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableApplicationLayerAutomaticResponseWithContext", reflect.TypeOf((*MockShield)(nil).DisableApplicationLayerAutomaticResponseWithContext), varargs...)
}

// DisableProactiveEngagement mocks base method.
func (m *MockShield) DisableProactiveEngagement(arg0 *shield.DisableProactiveEngagementInput) (*shield.DisableProactiveEngagementOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableProactiveEngagement", arg0)
	ret0, _ := ret[0].(*shield.DisableProactiveEngagementOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableProactiveEngagement indicates an expected call of DisableProactiveEngagement.
func (mr *MockShieldMockRecorder) DisableProactiveEngagement(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableProactiveEngagement", reflect.TypeOf((*MockShield)(nil).DisableProactiveEngagement), arg0)
}

// DisableProactiveEngagementRequest mocks base method.
func (m *MockShield) DisableProactiveEngagementRequest(arg0 *shield.DisableProactiveEngagementInput) (*request.Request, *shield.DisableProactiveEngagementOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableProactiveEngagementRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DisableProactiveEngagementOutput)
	return ret0, ret1
}

// DisableProactiveEngagementRequest indicates an expected call of DisableProactiveEngagementRequest.
func (mr *MockShieldMockRecorder) DisableProactiveEngagementRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableProactiveEngagementRequest", reflect.TypeOf((*MockShield)(nil).DisableProactiveEngagementRequest), arg0)
}

// DisableProactiveEngagementWithContext mocks base method.
func (m *MockShield) DisableProactiveEngagementWithContext(arg0 context.Context, arg1 *shield.DisableProactiveEngagementInput, arg2 ...request.Option) (*shield.DisableProactiveEngagementOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisableProactiveEngagementWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DisableProactiveEngagementOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableProactiveEngagementWithContext indicates an expected call of DisableProactiveEngagementWithContext.
func (mr *MockShieldMockRecorder) DisableProactiveEngagementWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableProactiveEngagementWithContext", reflect.TypeOf((*MockShield)(nil).DisableProactiveEngagementWithContext), varargs...)
}

// DisassociateDRTLogBucket mocks base method.
func (m *MockShield) DisassociateDRTLogBucket(arg0 *shield.DisassociateDRTLogBucketInput) (*shield.DisassociateDRTLogBucketOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisassociateDRTLogBucket", arg0)
	ret0, _ := ret[0].(*shield.DisassociateDRTLogBucketOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisassociateDRTLogBucket indicates an expected call of DisassociateDRTLogBucket.
func (mr *MockShieldMockRecorder) DisassociateDRTLogBucket(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateDRTLogBucket", reflect.TypeOf((*MockShield)(nil).DisassociateDRTLogBucket), arg0)
}

// DisassociateDRTLogBucketRequest mocks base method.
func (m *MockShield) DisassociateDRTLogBucketRequest(arg0 *shield.DisassociateDRTLogBucketInput) (*request.Request, *shield.DisassociateDRTLogBucketOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisassociateDRTLogBucketRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DisassociateDRTLogBucketOutput)
	return ret0, ret1
}

// DisassociateDRTLogBucketRequest indicates an expected call of DisassociateDRTLogBucketRequest.
func (mr *MockShieldMockRecorder) DisassociateDRTLogBucketRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateDRTLogBucketRequest", reflect.TypeOf((*MockShield)(nil).DisassociateDRTLogBucketRequest), arg0)
}

// DisassociateDRTLogBucketWithContext mocks base method.
func (m *MockShield) DisassociateDRTLogBucketWithContext(arg0 context.Context, arg1 *shield.DisassociateDRTLogBucketInput, arg2 ...request.Option) (*shield.DisassociateDRTLogBucketOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisassociateDRTLogBucketWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DisassociateDRTLogBucketOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisassociateDRTLogBucketWithContext indicates an expected call of DisassociateDRTLogBucketWithContext.
func (mr *MockShieldMockRecorder) DisassociateDRTLogBucketWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateDRTLogBucketWithContext", reflect.TypeOf((*MockShield)(nil).DisassociateDRTLogBucketWithContext), varargs...)
}

// DisassociateDRTRole mocks base method.
func (m *MockShield) DisassociateDRTRole(arg0 *shield.DisassociateDRTRoleInput) (*shield.DisassociateDRTRoleOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisassociateDRTRole", arg0)
	ret0, _ := ret[0].(*shield.DisassociateDRTRoleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisassociateDRTRole indicates an expected call of DisassociateDRTRole.
func (mr *MockShieldMockRecorder) DisassociateDRTRole(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateDRTRole", reflect.TypeOf((*MockShield)(nil).DisassociateDRTRole), arg0)
}

// DisassociateDRTRoleRequest mocks base method.
func (m *MockShield) DisassociateDRTRoleRequest(arg0 *shield.DisassociateDRTRoleInput) (*request.Request, *shield.DisassociateDRTRoleOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisassociateDRTRoleRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DisassociateDRTRoleOutput)
	return ret0, ret1
}

// DisassociateDRTRoleRequest indicates an expected call of DisassociateDRTRoleRequest.
func (mr *MockShieldMockRecorder) DisassociateDRTRoleRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateDRTRoleRequest", reflect.TypeOf((*MockShield)(nil).DisassociateDRTRoleRequest), arg0)
}

// DisassociateDRTRoleWithContext mocks base method.
func (m *MockShield) DisassociateDRTRoleWithContext(arg0 context.Context, arg1 *shield.DisassociateDRTRoleInput, arg2 ...request.Option) (*shield.DisassociateDRTRoleOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisassociateDRTRoleWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DisassociateDRTRoleOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisassociateDRTRoleWithContext indicates an expected call of DisassociateDRTRoleWithContext.
func (mr *MockShieldMockRecorder) DisassociateDRTRoleWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateDRTRoleWithContext", reflect.TypeOf((*MockShield)(nil).DisassociateDRTRoleWithContext), varargs...)
}

// DisassociateHealthCheck mocks base method.
func (m *MockShield) DisassociateHealthCheck(arg0 *shield.DisassociateHealthCheckInput) (*shield.DisassociateHealthCheckOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisassociateHealthCheck", arg0)
	ret0, _ := ret[0].(*shield.DisassociateHealthCheckOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisassociateHealthCheck indicates an expected call of DisassociateHealthCheck.
func (mr *MockShieldMockRecorder) DisassociateHealthCheck(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateHealthCheck", reflect.TypeOf((*MockShield)(nil).DisassociateHealthCheck), arg0)
}

// DisassociateHealthCheckRequest mocks base method.
func (m *MockShield) DisassociateHealthCheckRequest(arg0 *shield.DisassociateHealthCheckInput) (*request.Request, *shield.DisassociateHealthCheckOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisassociateHealthCheckRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.DisassociateHealthCheckOutput)
	return ret0, ret1
}

// DisassociateHealthCheckRequest indicates an expected call of DisassociateHealthCheckRequest.
func (mr *MockShieldMockRecorder) DisassociateHealthCheckRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateHealthCheckRequest", reflect.TypeOf((*MockShield)(nil).DisassociateHealthCheckRequest), arg0)
}

// DisassociateHealthCheckWithContext mocks base method.
func (m *MockShield) DisassociateHealthCheckWithContext(arg0 context.Context, arg1 *shield.DisassociateHealthCheckInput, arg2 ...request.Option) (*shield.DisassociateHealthCheckOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisassociateHealthCheckWithContext", varargs...)
	ret0, _ := ret[0].(*shield.DisassociateHealthCheckOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisassociateHealthCheckWithContext indicates an expected call of DisassociateHealthCheckWithContext.
func (mr *MockShieldMockRecorder) DisassociateHealthCheckWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisassociateHealthCheckWithContext", reflect.TypeOf((*MockShield)(nil).DisassociateHealthCheckWithContext), varargs...)
}

// EnableApplicationLayerAutomaticResponse mocks base method.
func (m *MockShield) EnableApplicationLayerAutomaticResponse(arg0 *shield.EnableApplicationLayerAutomaticResponseInput) (*shield.EnableApplicationLayerAutomaticResponseOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableApplicationLayerAutomaticResponse", arg0)
	ret0, _ := ret[0].(*shield.EnableApplicationLayerAutomaticResponseOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableApplicationLayerAutomaticResponse indicates an expected call of EnableApplicationLayerAutomaticResponse.
func (mr *MockShieldMockRecorder) EnableApplicationLayerAutomaticResponse(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableApplicationLayerAutomaticResponse", reflect.TypeOf((*MockShield)(nil).EnableApplicationLayerAutomaticResponse), arg0)
}

// EnableApplicationLayerAutomaticResponseRequest mocks base method.
func (m *MockShield) EnableApplicationLayerAutomaticResponseRequest(arg0 *shield.EnableApplicationLayerAutomaticResponseInput) (*request.Request, *shield.EnableApplicationLayerAutomaticResponseOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableApplicationLayerAutomaticResponseRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.EnableApplicationLayerAutomaticResponseOutput)
	return ret0, ret1
}

// EnableApplicationLayerAutomaticResponseRequest indicates an expected call of EnableApplicationLayerAutomaticResponseRequest.
func (mr *MockShieldMockRecorder) EnableApplicationLayerAutomaticResponseRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableApplicationLayerAutomaticResponseRequest", reflect.TypeOf((*MockShield)(nil).EnableApplicationLayerAutomaticResponseRequest), arg0)
}

// EnableApplicationLayerAutomaticResponseWithContext mocks base method.
func (m *MockShield) EnableApplicationLayerAutomaticResponseWithContext(arg0 context.Context, arg1 *shield.EnableApplicationLayerAutomaticResponseInput, arg2 ...request.Option) (*shield.EnableApplicationLayerAutomaticResponseOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EnableApplicationLayerAutomaticResponseWithContext", varargs...)
	ret0, _ := ret[0].(*shield.EnableApplicationLayerAutomaticResponseOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableApplicationLayerAutomaticResponseWithContext indicates an expected call of EnableApplicationLayerAutomaticResponseWithContext.
func (mr *MockShieldMockRecorder) EnableApplicationLayerAutomaticResponseWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableApplicationLayerAutomaticResponseWithContext", reflect.TypeOf((*MockShield)(nil).EnableApplicationLayerAutomaticResponseWithContext), varargs...)
}

// EnableProactiveEngagement mocks base method.
func (m *MockShield) EnableProactiveEngagement(arg0 *shield.EnableProactiveEngagementInput) (*shield.EnableProactiveEngagementOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableProactiveEngagement", arg0)
	ret0, _ := ret[0].(*shield.EnableProactiveEngagementOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableProactiveEngagement indicates an expected call of EnableProactiveEngagement.
func (mr *MockShieldMockRecorder) EnableProactiveEngagement(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableProactiveEngagement", reflect.TypeOf((*MockShield)(nil).EnableProactiveEngagement), arg0)
}

// EnableProactiveEngagementRequest mocks base method.
func (m *MockShield) EnableProactiveEngagementRequest(arg0 *shield.EnableProactiveEngagementInput) (*request.Request, *shield.EnableProactiveEngagementOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableProactiveEngagementRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.EnableProactiveEngagementOutput)
	return ret0, ret1
}

// EnableProactiveEngagementRequest indicates an expected call of EnableProactiveEngagementRequest.
func (mr *MockShieldMockRecorder) EnableProactiveEngagementRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableProactiveEngagementRequest", reflect.TypeOf((*MockShield)(nil).EnableProactiveEngagementRequest), arg0)
}

// EnableProactiveEngagementWithContext mocks base method.
func (m *MockShield) EnableProactiveEngagementWithContext(arg0 context.Context, arg1 *shield.EnableProactiveEngagementInput, arg2 ...request.Option) (*shield.EnableProactiveEngagementOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EnableProactiveEngagementWithContext", varargs...)
	ret0, _ := ret[0].(*shield.EnableProactiveEngagementOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableProactiveEngagementWithContext indicates an expected call of EnableProactiveEngagementWithContext.
func (mr *MockShieldMockRecorder) EnableProactiveEngagementWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableProactiveEngagementWithContext", reflect.TypeOf((*MockShield)(nil).EnableProactiveEngagementWithContext), varargs...)
}

// GetSubscriptionState mocks base method.
func (m *MockShield) GetSubscriptionState(arg0 *shield.GetSubscriptionStateInput) (*shield.GetSubscriptionStateOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubscriptionState", arg0)
	ret0, _ := ret[0].(*shield.GetSubscriptionStateOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubscriptionState indicates an expected call of GetSubscriptionState.
func (mr *MockShieldMockRecorder) GetSubscriptionState(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubscriptionState", reflect.TypeOf((*MockShield)(nil).GetSubscriptionState), arg0)
}

// GetSubscriptionStateRequest mocks base method.
func (m *MockShield) GetSubscriptionStateRequest(arg0 *shield.GetSubscriptionStateInput) (*request.Request, *shield.GetSubscriptionStateOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubscriptionStateRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.GetSubscriptionStateOutput)
	return ret0, ret1
}

// GetSubscriptionStateRequest indicates an expected call of GetSubscriptionStateRequest.
func (mr *MockShieldMockRecorder) GetSubscriptionStateRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubscriptionStateRequest", reflect.TypeOf((*MockShield)(nil).GetSubscriptionStateRequest), arg0)
}

// GetSubscriptionStateWithContext mocks base method.
func (m *MockShield) GetSubscriptionStateWithContext(arg0 context.Context, arg1 *shield.GetSubscriptionStateInput, arg2 ...request.Option) (*shield.GetSubscriptionStateOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSubscriptionStateWithContext", varargs...)
	ret0, _ := ret[0].(*shield.GetSubscriptionStateOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubscriptionStateWithContext indicates an expected call of GetSubscriptionStateWithContext.
func (mr *MockShieldMockRecorder) GetSubscriptionStateWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubscriptionStateWithContext", reflect.TypeOf((*MockShield)(nil).GetSubscriptionStateWithContext), varargs...)
}

// ListAttacks mocks base method.
func (m *MockShield) ListAttacks(arg0 *shield.ListAttacksInput) (*shield.ListAttacksOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAttacks", arg0)
	ret0, _ := ret[0].(*shield.ListAttacksOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAttacks indicates an expected call of ListAttacks.
func (mr *MockShieldMockRecorder) ListAttacks(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAttacks", reflect.TypeOf((*MockShield)(nil).ListAttacks), arg0)
}

// ListAttacksPages mocks base method.
func (m *MockShield) ListAttacksPages(arg0 *shield.ListAttacksInput, arg1 func(*shield.ListAttacksOutput, bool) bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAttacksPages", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListAttacksPages indicates an expected call of ListAttacksPages.
func (mr *MockShieldMockRecorder) ListAttacksPages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAttacksPages", reflect.TypeOf((*MockShield)(nil).ListAttacksPages), arg0, arg1)
}

// ListAttacksPagesWithContext mocks base method.
func (m *MockShield) ListAttacksPagesWithContext(arg0 context.Context, arg1 *shield.ListAttacksInput, arg2 func(*shield.ListAttacksOutput, bool) bool, arg3 ...request.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAttacksPagesWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListAttacksPagesWithContext indicates an expected call of ListAttacksPagesWithContext.
func (mr *MockShieldMockRecorder) ListAttacksPagesWithContext(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAttacksPagesWithContext", reflect.TypeOf((*MockShield)(nil).ListAttacksPagesWithContext), varargs...)
}

// ListAttacksRequest mocks base method.
func (m *MockShield) ListAttacksRequest(arg0 *shield.ListAttacksInput) (*request.Request, *shield.ListAttacksOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAttacksRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.ListAttacksOutput)
	return ret0, ret1
}

// ListAttacksRequest indicates an expected call of ListAttacksRequest.
func (mr *MockShieldMockRecorder) ListAttacksRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAttacksRequest", reflect.TypeOf((*MockShield)(nil).ListAttacksRequest), arg0)
}

// ListAttacksWithContext mocks base method.
func (m *MockShield) ListAttacksWithContext(arg0 context.Context, arg1 *shield.ListAttacksInput, arg2 ...request.Option) (*shield.ListAttacksOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAttacksWithContext", varargs...)
	ret0, _ := ret[0].(*shield.ListAttacksOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAttacksWithContext indicates an expected call of ListAttacksWithContext.
func (mr *MockShieldMockRecorder) ListAttacksWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAttacksWithContext", reflect.TypeOf((*MockShield)(nil).ListAttacksWithContext), varargs...)
}

// ListProtectionGroups mocks base method.
func (m *MockShield) ListProtectionGroups(arg0 *shield.ListProtectionGroupsInput) (*shield.ListProtectionGroupsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProtectionGroups", arg0)
	ret0, _ := ret[0].(*shield.ListProtectionGroupsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProtectionGroups indicates an expected call of ListProtectionGroups.
func (mr *MockShieldMockRecorder) ListProtectionGroups(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionGroups", reflect.TypeOf((*MockShield)(nil).ListProtectionGroups), arg0)
}

// ListProtectionGroupsPages mocks base method.
func (m *MockShield) ListProtectionGroupsPages(arg0 *shield.ListProtectionGroupsInput, arg1 func(*shield.ListProtectionGroupsOutput, bool) bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProtectionGroupsPages", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListProtectionGroupsPages indicates an expected call of ListProtectionGroupsPages.
func (mr *MockShieldMockRecorder) ListProtectionGroupsPages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionGroupsPages", reflect.TypeOf((*MockShield)(nil).ListProtectionGroupsPages), arg0, arg1)
}

// ListProtectionGroupsPagesWithContext mocks base method.
func (m *MockShield) ListProtectionGroupsPagesWithContext(arg0 context.Context, arg1 *shield.ListProtectionGroupsInput, arg2 func(*shield.ListProtectionGroupsOutput, bool) bool, arg3 ...request.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListProtectionGroupsPagesWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListProtectionGroupsPagesWithContext indicates an expected call of ListProtectionGroupsPagesWithContext.
func (mr *MockShieldMockRecorder) ListProtectionGroupsPagesWithContext(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionGroupsPagesWithContext", reflect.TypeOf((*MockShield)(nil).ListProtectionGroupsPagesWithContext), varargs...)
}

// ListProtectionGroupsRequest mocks base method.
func (m *MockShield) ListProtectionGroupsRequest(arg0 *shield.ListProtectionGroupsInput) (*request.Request, *shield.ListProtectionGroupsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProtectionGroupsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.ListProtectionGroupsOutput)
	return ret0, ret1
}

// ListProtectionGroupsRequest indicates an expected call of ListProtectionGroupsRequest.
func (mr *MockShieldMockRecorder) ListProtectionGroupsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionGroupsRequest", reflect.TypeOf((*MockShield)(nil).ListProtectionGroupsRequest), arg0)
}

// ListProtectionGroupsWithContext mocks base method.
func (m *MockShield) ListProtectionGroupsWithContext(arg0 context.Context, arg1 *shield.ListProtectionGroupsInput, arg2 ...request.Option) (*shield.ListProtectionGroupsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListProtectionGroupsWithContext", varargs...)
	ret0, _ := ret[0].(*shield.ListProtectionGroupsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProtectionGroupsWithContext indicates an expected call of ListProtectionGroupsWithContext.
func (mr *MockShieldMockRecorder) ListProtectionGroupsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionGroupsWithContext", reflect.TypeOf((*MockShield)(nil).ListProtectionGroupsWithContext), varargs...)
}

// ListProtections mocks base method.
func (m *MockShield) ListProtections(arg0 *shield.ListProtectionsInput) (*shield.ListProtectionsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProtections", arg0)
	ret0, _ := ret[0].(*shield.ListProtectionsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProtections indicates an expected call of ListProtections.
func (mr *MockShieldMockRecorder) ListProtections(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtections", reflect.TypeOf((*MockShield)(nil).ListProtections), arg0)
}

// ListProtectionsPages mocks base method.
func (m *MockShield) ListProtectionsPages(arg0 *shield.ListProtectionsInput, arg1 func(*shield.ListProtectionsOutput, bool) bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProtectionsPages", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListProtectionsPages indicates an expected call of ListProtectionsPages.
func (mr *MockShieldMockRecorder) ListProtectionsPages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionsPages", reflect.TypeOf((*MockShield)(nil).ListProtectionsPages), arg0, arg1)
}

// ListProtectionsPagesWithContext mocks base method.
func (m *MockShield) ListProtectionsPagesWithContext(arg0 context.Context, arg1 *shield.ListProtectionsInput, arg2 func(*shield.ListProtectionsOutput, bool) bool, arg3 ...request.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListProtectionsPagesWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListProtectionsPagesWithContext indicates an expected call of ListProtectionsPagesWithContext.
func (mr *MockShieldMockRecorder) ListProtectionsPagesWithContext(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionsPagesWithContext", reflect.TypeOf((*MockShield)(nil).ListProtectionsPagesWithContext), varargs...)
}

// ListProtectionsRequest mocks base method.
func (m *MockShield) ListProtectionsRequest(arg0 *shield.ListProtectionsInput) (*request.Request, *shield.ListProtectionsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProtectionsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.ListProtectionsOutput)
	return ret0, ret1
}

// ListProtectionsRequest indicates an expected call of ListProtectionsRequest.
func (mr *MockShieldMockRecorder) ListProtectionsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionsRequest", reflect.TypeOf((*MockShield)(nil).ListProtectionsRequest), arg0)
}

// ListProtectionsWithContext mocks base method.
func (m *MockShield) ListProtectionsWithContext(arg0 context.Context, arg1 *shield.ListProtectionsInput, arg2 ...request.Option) (*shield.ListProtectionsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListProtectionsWithContext", varargs...)
	ret0, _ := ret[0].(*shield.ListProtectionsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProtectionsWithContext indicates an expected call of ListProtectionsWithContext.
func (mr *MockShieldMockRecorder) ListProtectionsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProtectionsWithContext", reflect.TypeOf((*MockShield)(nil).ListProtectionsWithContext), varargs...)
}

// ListResourcesInProtectionGroup mocks base method.
func (m *MockShield) ListResourcesInProtectionGroup(arg0 *shield.ListResourcesInProtectionGroupInput) (*shield.ListResourcesInProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResourcesInProtectionGroup", arg0)
	ret0, _ := ret[0].(*shield.ListResourcesInProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResourcesInProtectionGroup indicates an expected call of ListResourcesInProtectionGroup.
func (mr *MockShieldMockRecorder) ListResourcesInProtectionGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResourcesInProtectionGroup", reflect.TypeOf((*MockShield)(nil).ListResourcesInProtectionGroup), arg0)
}

// ListResourcesInProtectionGroupPages mocks base method.
func (m *MockShield) ListResourcesInProtectionGroupPages(arg0 *shield.ListResourcesInProtectionGroupInput, arg1 func(*shield.ListResourcesInProtectionGroupOutput, bool) bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResourcesInProtectionGroupPages", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListResourcesInProtectionGroupPages indicates an expected call of ListResourcesInProtectionGroupPages.
func (mr *MockShieldMockRecorder) ListResourcesInProtectionGroupPages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResourcesInProtectionGroupPages", reflect.TypeOf((*MockShield)(nil).ListResourcesInProtectionGroupPages), arg0, arg1)
}

// ListResourcesInProtectionGroupPagesWithContext mocks base method.
func (m *MockShield) ListResourcesInProtectionGroupPagesWithContext(arg0 context.Context, arg1 *shield.ListResourcesInProtectionGroupInput, arg2 func(*shield.ListResourcesInProtectionGroupOutput, bool) bool, arg3 ...request.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListResourcesInProtectionGroupPagesWithContext", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// ListResourcesInProtectionGroupPagesWithContext indicates an expected call of ListResourcesInProtectionGroupPagesWithContext.
func (mr *MockShieldMockRecorder) ListResourcesInProtectionGroupPagesWithContext(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResourcesInProtectionGroupPagesWithContext", reflect.TypeOf((*MockShield)(nil).ListResourcesInProtectionGroupPagesWithContext), varargs...)
}

// ListResourcesInProtectionGroupRequest mocks base method.
func (m *MockShield) ListResourcesInProtectionGroupRequest(arg0 *shield.ListResourcesInProtectionGroupInput) (*request.Request, *shield.ListResourcesInProtectionGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListResourcesInProtectionGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.ListResourcesInProtectionGroupOutput)
	return ret0, ret1
}

// ListResourcesInProtectionGroupRequest indicates an expected call of ListResourcesInProtectionGroupRequest.
func (mr *MockShieldMockRecorder) ListResourcesInProtectionGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResourcesInProtectionGroupRequest", reflect.TypeOf((*MockShield)(nil).ListResourcesInProtectionGroupRequest), arg0)
}

// ListResourcesInProtectionGroupWithContext mocks base method.
func (m *MockShield) ListResourcesInProtectionGroupWithContext(arg0 context.Context, arg1 *shield.ListResourcesInProtectionGroupInput, arg2 ...request.Option) (*shield.ListResourcesInProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListResourcesInProtectionGroupWithContext", varargs...)
	ret0, _ := ret[0].(*shield.ListResourcesInProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListResourcesInProtectionGroupWithContext indicates an expected call of ListResourcesInProtectionGroupWithContext.
func (mr *MockShieldMockRecorder) ListResourcesInProtectionGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListResourcesInProtectionGroupWithContext", reflect.TypeOf((*MockShield)(nil).ListResourcesInProtectionGroupWithContext), varargs...)
}

// ListTagsForResource mocks base method.
func (m *MockShield) ListTagsForResource(arg0 *shield.ListTagsForResourceInput) (*shield.ListTagsForResourceOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTagsForResource", arg0)
	ret0, _ := ret[0].(*shield.ListTagsForResourceOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTagsForResource indicates an expected call of ListTagsForResource.
func (mr *MockShieldMockRecorder) ListTagsForResource(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTagsForResource", reflect.TypeOf((*MockShield)(nil).ListTagsForResource), arg0)
}

// ListTagsForResourceRequest mocks base method.
func (m *MockShield) ListTagsForResourceRequest(arg0 *shield.ListTagsForResourceInput) (*request.Request, *shield.ListTagsForResourceOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTagsForResourceRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.ListTagsForResourceOutput)
	return ret0, ret1
}

// ListTagsForResourceRequest indicates an expected call of ListTagsForResourceRequest.
func (mr *MockShieldMockRecorder) ListTagsForResourceRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTagsForResourceRequest", reflect.TypeOf((*MockShield)(nil).ListTagsForResourceRequest), arg0)
}

// ListTagsForResourceWithContext mocks base method.
func (m *MockShield) ListTagsForResourceWithContext(arg0 context.Context, arg1 *shield.ListTagsForResourceInput, arg2 ...request.Option) (*shield.ListTagsForResourceOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListTagsForResourceWithContext", varargs...)
	ret0, _ := ret[0].(*shield.ListTagsForResourceOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTagsForResourceWithContext indicates an expected call of ListTagsForResourceWithContext.
func (mr *MockShieldMockRecorder) ListTagsForResourceWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTagsForResourceWithContext", reflect.TypeOf((*MockShield)(nil).ListTagsForResourceWithContext), varargs...)
}

// TagResource mocks base method.
func (m *MockShield) TagResource(arg0 *shield.TagResourceInput) (*shield.TagResourceOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TagResource", arg0)
	ret0, _ := ret[0].(*shield.TagResourceOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TagResource indicates an expected call of TagResource.
func (mr *MockShieldMockRecorder) TagResource(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TagResource", reflect.TypeOf((*MockShield)(nil).TagResource), arg0)
}

// TagResourceRequest mocks base method.
func (m *MockShield) TagResourceRequest(arg0 *shield.TagResourceInput) (*request.Request, *shield.TagResourceOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TagResourceRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.TagResourceOutput)
	return ret0, ret1
}

// TagResourceRequest indicates an expected call of TagResourceRequest.
func (mr *MockShieldMockRecorder) TagResourceRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TagResourceRequest", reflect.TypeOf((*MockShield)(nil).TagResourceRequest), arg0)
}

// TagResourceWithContext mocks base method.
func (m *MockShield) TagResourceWithContext(arg0 context.Context, arg1 *shield.TagResourceInput, arg2 ...request.Option) (*shield.TagResourceOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TagResourceWithContext", varargs...)
	ret0, _ := ret[0].(*shield.TagResourceOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TagResourceWithContext indicates an expected call of TagResourceWithContext.
func (mr *MockShieldMockRecorder) TagResourceWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TagResourceWithContext", reflect.TypeOf((*MockShield)(nil).TagResourceWithContext), varargs...)
}

// UntagResource mocks base method.
func (m *MockShield) UntagResource(arg0 *shield.UntagResourceInput) (*shield.UntagResourceOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UntagResource", arg0)
	ret0, _ := ret[0].(*shield.UntagResourceOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UntagResource indicates an expected call of UntagResource.
func (mr *MockShieldMockRecorder) UntagResource(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UntagResource", reflect.TypeOf((*MockShield)(nil).UntagResource), arg0)
}

// UntagResourceRequest mocks base method.
func (m *MockShield) UntagResourceRequest(arg0 *shield.UntagResourceInput) (*request.Request, *shield.UntagResourceOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UntagResourceRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.UntagResourceOutput)
	return ret0, ret1
}

// UntagResourceRequest indicates an expected call of UntagResourceRequest.
func (mr *MockShieldMockRecorder) UntagResourceRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UntagResourceRequest", reflect.TypeOf((*MockShield)(nil).UntagResourceRequest), arg0)
}

// UntagResourceWithContext mocks base method.
func (m *MockShield) UntagResourceWithContext(arg0 context.Context, arg1 *shield.UntagResourceInput, arg2 ...request.Option) (*shield.UntagResourceOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UntagResourceWithContext", varargs...)
	ret0, _ := ret[0].(*shield.UntagResourceOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UntagResourceWithContext indicates an expected call of UntagResourceWithContext.
func (mr *MockShieldMockRecorder) UntagResourceWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UntagResourceWithContext", reflect.TypeOf((*MockShield)(nil).UntagResourceWithContext), varargs...)
}

// UpdateApplicationLayerAutomaticResponse mocks base method.
func (m *MockShield) UpdateApplicationLayerAutomaticResponse(arg0 *shield.UpdateApplicationLayerAutomaticResponseInput) (*shield.UpdateApplicationLayerAutomaticResponseOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateApplicationLayerAutomaticResponse", arg0)
	ret0, _ := ret[0].(*shield.UpdateApplicationLayerAutomaticResponseOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplicationLayerAutomaticResponse indicates an expected call of UpdateApplicationLayerAutomaticResponse.
func (mr *MockShieldMockRecorder) UpdateApplicationLayerAutomaticResponse(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicationLayerAutomaticResponse", reflect.TypeOf((*MockShield)(nil).UpdateApplicationLayerAutomaticResponse), arg0)
}

// UpdateApplicationLayerAutomaticResponseRequest mocks base method.
func (m *MockShield) UpdateApplicationLayerAutomaticResponseRequest(arg0 *shield.UpdateApplicationLayerAutomaticResponseInput) (*request.Request, *shield.UpdateApplicationLayerAutomaticResponseOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateApplicationLayerAutomaticResponseRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.UpdateApplicationLayerAutomaticResponseOutput)
	return ret0, ret1
}

// UpdateApplicationLayerAutomaticResponseRequest indicates an expected call of UpdateApplicationLayerAutomaticResponseRequest.
func (mr *MockShieldMockRecorder) UpdateApplicationLayerAutomaticResponseRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicationLayerAutomaticResponseRequest", reflect.TypeOf((*MockShield)(nil).UpdateApplicationLayerAutomaticResponseRequest), arg0)
}

// UpdateApplicationLayerAutomaticResponseWithContext mocks base method.
func (m *MockShield) UpdateApplicationLayerAutomaticResponseWithContext(arg0 context.Context, arg1 *shield.UpdateApplicationLayerAutomaticResponseInput, arg2 ...request.Option) (*shield.UpdateApplicationLayerAutomaticResponseOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateApplicationLayerAutomaticResponseWithContext", varargs...)
	ret0, _ := ret[0].(*shield.UpdateApplicationLayerAutomaticResponseOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplicationLayerAutomaticResponseWithContext indicates an expected call of UpdateApplicationLayerAutomaticResponseWithContext.
func (mr *MockShieldMockRecorder) UpdateApplicationLayerAutomaticResponseWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplicationLayerAutomaticResponseWithContext", reflect.TypeOf((*MockShield)(nil).UpdateApplicationLayerAutomaticResponseWithContext), varargs...)
}

// UpdateEmergencyContactSettings mocks base method.
func (m *MockShield) UpdateEmergencyContactSettings(arg0 *shield.UpdateEmergencyContactSettingsInput) (*shield.UpdateEmergencyContactSettingsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEmergencyContactSettings", arg0)
	ret0, _ := ret[0].(*shield.UpdateEmergencyContactSettingsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEmergencyContactSettings indicates an expected call of UpdateEmergencyContactSettings.
func (mr *MockShieldMockRecorder) UpdateEmergencyContactSettings(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEmergencyContactSettings", reflect.TypeOf((*MockShield)(nil).UpdateEmergencyContactSettings), arg0)
}

// UpdateEmergencyContactSettingsRequest mocks base method.
func (m *MockShield) UpdateEmergencyContactSettingsRequest(arg0 *shield.UpdateEmergencyContactSettingsInput) (*request.Request, *shield.UpdateEmergencyContactSettingsOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEmergencyContactSettingsRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.UpdateEmergencyContactSettingsOutput)
	return ret0, ret1
}

// UpdateEmergencyContactSettingsRequest indicates an expected call of UpdateEmergencyContactSettingsRequest.
func (mr *MockShieldMockRecorder) UpdateEmergencyContactSettingsRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEmergencyContactSettingsRequest", reflect.TypeOf((*MockShield)(nil).UpdateEmergencyContactSettingsRequest), arg0)
}

// UpdateEmergencyContactSettingsWithContext mocks base method.
func (m *MockShield) UpdateEmergencyContactSettingsWithContext(arg0 context.Context, arg1 *shield.UpdateEmergencyContactSettingsInput, arg2 ...request.Option) (*shield.UpdateEmergencyContactSettingsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEmergencyContactSettingsWithContext", varargs...)
	ret0, _ := ret[0].(*shield.UpdateEmergencyContactSettingsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEmergencyContactSettingsWithContext indicates an expected call of UpdateEmergencyContactSettingsWithContext.
func (mr *MockShieldMockRecorder) UpdateEmergencyContactSettingsWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEmergencyContactSettingsWithContext", reflect.TypeOf((*MockShield)(nil).UpdateEmergencyContactSettingsWithContext), varargs...)
}

// UpdateProtectionGroup mocks base method.
func (m *MockShield) UpdateProtectionGroup(arg0 *shield.UpdateProtectionGroupInput) (*shield.UpdateProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProtectionGroup", arg0)
	ret0, _ := ret[0].(*shield.UpdateProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateProtectionGroup indicates an expected call of UpdateProtectionGroup.
func (mr *MockShieldMockRecorder) UpdateProtectionGroup(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProtectionGroup", reflect.TypeOf((*MockShield)(nil).UpdateProtectionGroup), arg0)
}

// UpdateProtectionGroupRequest mocks base method.
func (m *MockShield) UpdateProtectionGroupRequest(arg0 *shield.UpdateProtectionGroupInput) (*request.Request, *shield.UpdateProtectionGroupOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProtectionGroupRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.UpdateProtectionGroupOutput)
	return ret0, ret1
}

// UpdateProtectionGroupRequest indicates an expected call of UpdateProtectionGroupRequest.
func (mr *MockShieldMockRecorder) UpdateProtectionGroupRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProtectionGroupRequest", reflect.TypeOf((*MockShield)(nil).UpdateProtectionGroupRequest), arg0)
}

// UpdateProtectionGroupWithContext mocks base method.
func (m *MockShield) UpdateProtectionGroupWithContext(arg0 context.Context, arg1 *shield.UpdateProtectionGroupInput, arg2 ...request.Option) (*shield.UpdateProtectionGroupOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateProtectionGroupWithContext", varargs...)
	ret0, _ := ret[0].(*shield.UpdateProtectionGroupOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateProtectionGroupWithContext indicates an expected call of UpdateProtectionGroupWithContext.
func (mr *MockShieldMockRecorder) UpdateProtectionGroupWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProtectionGroupWithContext", reflect.TypeOf((*MockShield)(nil).UpdateProtectionGroupWithContext), varargs...)
}

// UpdateSubscription mocks base method.
func (m *MockShield) UpdateSubscription(arg0 *shield.UpdateSubscriptionInput) (*shield.UpdateSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSubscription", arg0)
	ret0, _ := ret[0].(*shield.UpdateSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSubscription indicates an expected call of UpdateSubscription.
func (mr *MockShieldMockRecorder) UpdateSubscription(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSubscription", reflect.TypeOf((*MockShield)(nil).UpdateSubscription), arg0)
}

// UpdateSubscriptionRequest mocks base method.
func (m *MockShield) UpdateSubscriptionRequest(arg0 *shield.UpdateSubscriptionInput) (*request.Request, *shield.UpdateSubscriptionOutput) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSubscriptionRequest", arg0)
	ret0, _ := ret[0].(*request.Request)
	ret1, _ := ret[1].(*shield.UpdateSubscriptionOutput)
	return ret0, ret1
}

// UpdateSubscriptionRequest indicates an expected call of UpdateSubscriptionRequest.
func (mr *MockShieldMockRecorder) UpdateSubscriptionRequest(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSubscriptionRequest", reflect.TypeOf((*MockShield)(nil).UpdateSubscriptionRequest), arg0)
}

// UpdateSubscriptionWithContext mocks base method.
func (m *MockShield) UpdateSubscriptionWithContext(arg0 context.Context, arg1 *shield.UpdateSubscriptionInput, arg2 ...request.Option) (*shield.UpdateSubscriptionOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSubscriptionWithContext", varargs...)
	ret0, _ := ret[0].(*shield.UpdateSubscriptionOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSubscriptionWithContext indicates an expected call of UpdateSubscriptionWithContext.
func (mr *MockShieldMockRecorder) UpdateSubscriptionWithContext(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSubscriptionWithContext", reflect.TypeOf((*MockShield)(nil).UpdateSubscriptionWithContext), varargs...)
}
