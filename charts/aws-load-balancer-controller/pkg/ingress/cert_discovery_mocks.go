// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/ingress (interfaces: CertDiscovery)

// Package ingress is a generated GoMock package.
package ingress

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockCertDiscovery is a mock of CertDiscovery interface.
type MockCertDiscovery struct {
	ctrl     *gomock.Controller
	recorder *MockCertDiscoveryMockRecorder
}

// MockCertDiscoveryMockRecorder is the mock recorder for MockCertDiscovery.
type MockCertDiscoveryMockRecorder struct {
	mock *MockCertDiscovery
}

// NewMockCertDiscovery creates a new mock instance.
func NewMockCertDiscovery(ctrl *gomock.Controller) *MockCertDiscovery {
	mock := &MockCertDiscovery{ctrl: ctrl}
	mock.recorder = &MockCertDiscoveryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCertDiscovery) EXPECT() *MockCertDiscoveryMockRecorder {
	return m.recorder
}

// Discover mocks base method.
func (m *MockCertDiscovery) Discover(arg0 context.Context, arg1 []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Discover", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Discover indicates an expected call of Discover.
func (mr *MockCertDiscoveryMockRecorder) Discover(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Discover", reflect.TypeOf((*MockCertDiscovery)(nil).Discover), arg0, arg1)
}
