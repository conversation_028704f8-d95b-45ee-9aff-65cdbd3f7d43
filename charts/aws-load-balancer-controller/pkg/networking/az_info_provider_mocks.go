// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/networking (interfaces: AZInfoProvider)

// Package networking is a generated GoMock package.
package networking

import (
	context "context"
	reflect "reflect"

	ec2 "github.com/aws/aws-sdk-go/service/ec2"
	gomock "github.com/golang/mock/gomock"
)

// MockAZInfoProvider is a mock of AZInfoProvider interface.
type MockAZInfoProvider struct {
	ctrl     *gomock.Controller
	recorder *MockAZInfoProviderMockRecorder
}

// MockAZInfoProviderMockRecorder is the mock recorder for MockAZInfoProvider.
type MockAZInfoProviderMockRecorder struct {
	mock *MockAZInfoProvider
}

// NewMockAZInfoProvider creates a new mock instance.
func NewMockAZInfoProvider(ctrl *gomock.Controller) *MockAZInfoProvider {
	mock := &MockAZInfoProvider{ctrl: ctrl}
	mock.recorder = &MockAZInfoProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAZInfoProvider) EXPECT() *MockAZInfoProviderMockRecorder {
	return m.recorder
}

// FetchAZInfos mocks base method.
func (m *MockAZInfoProvider) FetchAZInfos(arg0 context.Context, arg1 []string) (map[string]ec2.AvailabilityZone, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAZInfos", arg0, arg1)
	ret0, _ := ret[0].(map[string]ec2.AvailabilityZone)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAZInfos indicates an expected call of FetchAZInfos.
func (mr *MockAZInfoProviderMockRecorder) FetchAZInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAZInfos", reflect.TypeOf((*MockAZInfoProvider)(nil).FetchAZInfos), arg0, arg1)
}
