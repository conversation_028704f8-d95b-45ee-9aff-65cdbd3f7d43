package networking

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	awssdk "github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	ec2sdk "github.com/aws/aws-sdk-go/service/ec2"
	"github.com/go-logr/logr"
	"github.com/pkg/errors"
	networking "k8s.io/api/networking/v1"
	"sigs.k8s.io/aws-load-balancer-controller/pkg/aws/services"
	"sigs.k8s.io/aws-load-balancer-controller/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	defaultSGDeletionPollInterval = 2 * time.Second
	defaultSGDeletionTimeout      = 2 * time.Minute

	resourceTypeSecurityGroup = "security-group"
	tagKeyK8sCluster          = "elbv2.k8s.aws/cluster"
	tagKeyResource            = "elbv2.k8s.aws/resource"
	tagValueBackend           = "backend-sg"

	explicitGroupFinalizerPrefix = "group.ingress.k8s.aws/"
	implicitGroupFinalizer       = "ingress.k8s.aws/resources"

	sgDescription = "[k8s] Shared Backend SecurityGroup for LoadBalancer"
)

// BackendSGProvider is responsible for providing backend security groups
type BackendSGProvider interface {
	// Get returns the backend security group to use
	Get(ctx context.Context) (string, error)
	// Release cleans up the auto-generated backend SG if necessary
	Release(ctx context.Context) error
}

// NewBackendSGProvider constructs a new  defaultBackendSGProvider
func NewBackendSGProvider(clusterName string, backendSG string, vpcID string,
	ec2Client services.EC2, k8sClient client.Client, defaultTags map[string]string, logger logr.Logger) *defaultBackendSGProvider {
	return &defaultBackendSGProvider{
		vpcID:       vpcID,
		clusterName: clusterName,
		backendSG:   backendSG,
		defaultTags: defaultTags,
		ec2Client:   ec2Client,
		k8sClient:   k8sClient,
		logger:      logger,
		mutex:       sync.Mutex{},

		defaultDeletionPollInterval: defaultSGDeletionPollInterval,
		defaultDeletionTimeout:      defaultSGDeletionTimeout,
	}
}

var _ BackendSGProvider = &defaultBackendSGProvider{}

type defaultBackendSGProvider struct {
	vpcID       string
	clusterName string
	mutex       sync.Mutex

	backendSG       string
	autoGeneratedSG string
	defaultTags     map[string]string
	ec2Client       services.EC2
	k8sClient       client.Client
	logger          logr.Logger

	defaultDeletionPollInterval time.Duration
	defaultDeletionTimeout      time.Duration
}

func (p *defaultBackendSGProvider) Get(ctx context.Context) (string, error) {
	if len(p.backendSG) > 0 {
		return p.backendSG, nil
	}
	// Auto generate Backend Security group, and return the id
	if err := p.allocateBackendSG(ctx); err != nil {
		p.logger.Error(err, "Failed to auto-create backend SG")
		return "", err
	}
	return p.autoGeneratedSG, nil
}

func (p *defaultBackendSGProvider) Release(ctx context.Context) error {
	if len(p.backendSG) > 0 {
		return nil
	}
	if required, err := p.isBackendSGRequired(ctx); required || err != nil {
		return err
	}
	return p.releaseSG(ctx)
}

func (p *defaultBackendSGProvider) allocateBackendSG(ctx context.Context) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(p.autoGeneratedSG) > 0 {
		return nil
	}

	sgName := p.getBackendSGName()
	sgID, err := p.getBackendSGFromEC2(ctx, sgName, p.vpcID)
	if err != nil {
		return err
	}
	if len(sgID) > 1 {
		p.logger.V(1).Info("Existing SG found", "id", sgID)
		p.autoGeneratedSG = sgID
		return nil
	}

	createReq := &ec2sdk.CreateSecurityGroupInput{
		VpcId:             awssdk.String(p.vpcID),
		GroupName:         awssdk.String(sgName),
		Description:       awssdk.String(sgDescription),
		TagSpecifications: p.buildBackendSGTags(ctx),
	}
	p.logger.Info("creating securityGroup", "name", sgName)
	resp, err := p.ec2Client.CreateSecurityGroupWithContext(ctx, createReq)
	if err != nil {
		return err
	}
	p.logger.V(1).Info("created SecurityGroup", "name", sgName, "id", resp.GroupId)
	p.autoGeneratedSG = awssdk.StringValue(resp.GroupId)
	return nil
}

func (p *defaultBackendSGProvider) buildBackendSGTags(_ context.Context) []*ec2sdk.TagSpecification {
	var defaultTags []*ec2sdk.Tag
	for key, val := range p.defaultTags {
		defaultTags = append(defaultTags, &ec2sdk.Tag{
			Key:   awssdk.String(key),
			Value: awssdk.String(val),
		})
	}
	sort.Slice(defaultTags, func(i, j int) bool {
		return awssdk.StringValue(defaultTags[i].Key) < awssdk.StringValue(defaultTags[j].Key)
	})
	return []*ec2sdk.TagSpecification{
		{
			ResourceType: awssdk.String(resourceTypeSecurityGroup),
			Tags: append(defaultTags, []*ec2sdk.Tag{
				{
					Key:   awssdk.String(tagKeyK8sCluster),
					Value: awssdk.String(p.clusterName),
				},
				{
					Key:   awssdk.String(tagKeyResource),
					Value: awssdk.String(tagValueBackend),
				},
			}...),
		},
	}
}

func (p *defaultBackendSGProvider) getBackendSGFromEC2(ctx context.Context, sgName string, vpcID string) (string, error) {
	req := &ec2sdk.DescribeSecurityGroupsInput{
		Filters: []*ec2sdk.Filter{
			{
				Name:   awssdk.String("vpc-id"),
				Values: awssdk.StringSlice([]string{vpcID}),
			},
			{
				Name:   awssdk.String(fmt.Sprintf("tag:%v", tagKeyK8sCluster)),
				Values: awssdk.StringSlice([]string{p.clusterName}),
			},
			{
				Name:   awssdk.String(fmt.Sprintf("tag:%v", tagKeyResource)),
				Values: awssdk.StringSlice([]string{tagValueBackend}),
			},
		},
	}
	p.logger.V(1).Info("Queriying existing SG", "vpc-id", vpcID, "name", sgName)
	sgs, err := p.ec2Client.DescribeSecurityGroupsAsList(ctx, req)
	if err != nil && !isEC2SecurityGroupNotFoundError(err) {
		return "", err
	}
	if len(sgs) > 0 {
		return awssdk.StringValue(sgs[0].GroupId), nil
	}
	return "", nil
}

func (p *defaultBackendSGProvider) isBackendSGRequired(ctx context.Context) (bool, error) {
	ingList := &networking.IngressList{}
	if err := p.k8sClient.List(ctx, ingList); err != nil {
		p.logger.Error(err, "Unable to list ingresses")
		return true, errors.Wrapf(err, "unable to list ingresses")
	}
	for _, ing := range ingList.Items {
		if !ing.DeletionTimestamp.IsZero() {
			continue
		}
		for _, fin := range ing.GetFinalizers() {
			if fin == implicitGroupFinalizer || strings.HasPrefix(fin, explicitGroupFinalizerPrefix) {
				return true, nil
			}
		}
	}
	p.logger.Info("No ingress found, backend SG can be deleted", "SG ID", p.autoGeneratedSG)
	return false, nil
}

func (p *defaultBackendSGProvider) releaseSG(ctx context.Context) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(p.autoGeneratedSG) == 0 {
		return nil
	}

	if required, err := p.isBackendSGRequired(ctx); required || err != nil {
		p.logger.V(1).Info("backend SG is required, releaseSG ignore delete")
		return err
	}
	req := &ec2sdk.DeleteSecurityGroupInput{
		GroupId: awssdk.String(p.autoGeneratedSG),
	}
	p.logger.V(1).Info("deleting default backend SG", "ID", p.autoGeneratedSG)
	if err := runtime.RetryImmediateOnError(p.defaultDeletionPollInterval, p.defaultDeletionTimeout, isSecurityGroupDependencyViolationError, func() error {
		_, err := p.ec2Client.DeleteSecurityGroupWithContext(ctx, req)
		return err
	}); err != nil {
		return errors.Wrap(err, "failed to delete securityGroup")
	}
	p.logger.Info("deleted securityGroup", "ID", p.autoGeneratedSG)

	p.autoGeneratedSG = ""
	return nil
}

var invalidSGNamePattern = regexp.MustCompile("[[:^alnum:]]")

func (p *defaultBackendSGProvider) getBackendSGName() string {
	sgNameHash := sha256.New()
	_, _ = sgNameHash.Write([]byte(p.clusterName))
	sgHash := hex.EncodeToString(sgNameHash.Sum(nil))
	sanitizedClusterName := invalidSGNamePattern.ReplaceAllString(p.clusterName, "")
	return fmt.Sprintf("k8s-traffic-%.232s-%.10s", sanitizedClusterName, sgHash)
}

func isSecurityGroupDependencyViolationError(err error) bool {
	var awsErr awserr.Error
	if errors.As(err, &awsErr) {
		return awsErr.Code() == "DependencyViolation"
	}
	return false
}

func isEC2SecurityGroupNotFoundError(err error) bool {
	var awsErr awserr.Error
	if errors.As(err, &awsErr) {
		return awsErr.Code() == "InvalidGroup.NotFound"
	}
	return false
}
