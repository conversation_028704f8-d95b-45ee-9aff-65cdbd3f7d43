// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/networking (interfaces: BackendSGProvider)

// Package networking is a generated GoMock package.
package networking

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockBackendSGProvider is a mock of BackendSGProvider interface.
type MockBackendSGProvider struct {
	ctrl     *gomock.Controller
	recorder *MockBackendSGProviderMockRecorder
}

// MockBackendSGProviderMockRecorder is the mock recorder for MockBackendSGProvider.
type MockBackendSGProviderMockRecorder struct {
	mock *MockBackendSGProvider
}

// NewMockBackendSGProvider creates a new mock instance.
func NewMockBackendSGProvider(ctrl *gomock.Controller) *MockBackendSGProvider {
	mock := &MockBackendSGProvider{ctrl: ctrl}
	mock.recorder = &MockBackendSGProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBackendSGProvider) EXPECT() *MockBackendSGProviderMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockBackendSGProvider) Get(arg0 context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockBackendSGProviderMockRecorder) Get(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockBackendSGProvider)(nil).Get), arg0)
}

// Release mocks base method.
func (m *MockBackendSGProvider) Release(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Release", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Release indicates an expected call of Release.
func (mr *MockBackendSGProviderMockRecorder) Release(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockBackendSGProvider)(nil).Release), arg0)
}
