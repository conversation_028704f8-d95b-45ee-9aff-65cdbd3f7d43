// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/networking (interfaces: SecurityGroupManager)

// Package networking is a generated GoMock package.
package networking

import (
	context "context"
	reflect "reflect"

	ec2 "github.com/aws/aws-sdk-go/service/ec2"
	gomock "github.com/golang/mock/gomock"
)

// MockSecurityGroupManager is a mock of SecurityGroupManager interface.
type MockSecurityGroupManager struct {
	ctrl     *gomock.Controller
	recorder *MockSecurityGroupManagerMockRecorder
}

// MockSecurityGroupManagerMockRecorder is the mock recorder for MockSecurityGroupManager.
type MockSecurityGroupManagerMockRecorder struct {
	mock *MockSecurityGroupManager
}

// NewMockSecurityGroupManager creates a new mock instance.
func NewMockSecurityGroupManager(ctrl *gomock.Controller) *MockSecurityGroupManager {
	mock := &MockSecurityGroupManager{ctrl: ctrl}
	mock.recorder = &MockSecurityGroupManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecurityGroupManager) EXPECT() *MockSecurityGroupManagerMockRecorder {
	return m.recorder
}

// AuthorizeSGIngress mocks base method.
func (m *MockSecurityGroupManager) AuthorizeSGIngress(arg0 context.Context, arg1 string, arg2 []IPPermissionInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthorizeSGIngress", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AuthorizeSGIngress indicates an expected call of AuthorizeSGIngress.
func (mr *MockSecurityGroupManagerMockRecorder) AuthorizeSGIngress(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthorizeSGIngress", reflect.TypeOf((*MockSecurityGroupManager)(nil).AuthorizeSGIngress), arg0, arg1, arg2)
}

// FetchSGInfosByID mocks base method.
func (m *MockSecurityGroupManager) FetchSGInfosByID(arg0 context.Context, arg1 []string, arg2 ...FetchSGInfoOption) (map[string]SecurityGroupInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchSGInfosByID", varargs...)
	ret0, _ := ret[0].(map[string]SecurityGroupInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSGInfosByID indicates an expected call of FetchSGInfosByID.
func (mr *MockSecurityGroupManagerMockRecorder) FetchSGInfosByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSGInfosByID", reflect.TypeOf((*MockSecurityGroupManager)(nil).FetchSGInfosByID), varargs...)
}

// FetchSGInfosByRequest mocks base method.
func (m *MockSecurityGroupManager) FetchSGInfosByRequest(arg0 context.Context, arg1 *ec2.DescribeSecurityGroupsInput) (map[string]SecurityGroupInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchSGInfosByRequest", arg0, arg1)
	ret0, _ := ret[0].(map[string]SecurityGroupInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSGInfosByRequest indicates an expected call of FetchSGInfosByRequest.
func (mr *MockSecurityGroupManagerMockRecorder) FetchSGInfosByRequest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSGInfosByRequest", reflect.TypeOf((*MockSecurityGroupManager)(nil).FetchSGInfosByRequest), arg0, arg1)
}

// RevokeSGIngress mocks base method.
func (m *MockSecurityGroupManager) RevokeSGIngress(arg0 context.Context, arg1 string, arg2 []IPPermissionInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeSGIngress", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevokeSGIngress indicates an expected call of RevokeSGIngress.
func (mr *MockSecurityGroupManagerMockRecorder) RevokeSGIngress(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeSGIngress", reflect.TypeOf((*MockSecurityGroupManager)(nil).RevokeSGIngress), arg0, arg1, arg2)
}
