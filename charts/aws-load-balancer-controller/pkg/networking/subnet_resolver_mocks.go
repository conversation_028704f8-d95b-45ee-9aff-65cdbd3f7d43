// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/networking (interfaces: SubnetsResolver)

// Package networking is a generated GoMock package.
package networking

import (
	context "context"
	reflect "reflect"

	ec2 "github.com/aws/aws-sdk-go/service/ec2"
	gomock "github.com/golang/mock/gomock"
)

// MockSubnetsResolver is a mock of SubnetsResolver interface.
type MockSubnetsResolver struct {
	ctrl     *gomock.Controller
	recorder *MockSubnetsResolverMockRecorder
}

// MockSubnetsResolverMockRecorder is the mock recorder for MockSubnetsResolver.
type MockSubnetsResolverMockRecorder struct {
	mock *MockSubnetsResolver
}

// NewMockSubnetsResolver creates a new mock instance.
func NewMockSubnetsResolver(ctrl *gomock.Controller) *MockSubnetsResolver {
	mock := &MockSubnetsResolver{ctrl: ctrl}
	mock.recorder = &MockSubnetsResolverMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSubnetsResolver) EXPECT() *MockSubnetsResolverMockRecorder {
	return m.recorder
}

// ResolveViaDiscovery mocks base method.
func (m *MockSubnetsResolver) ResolveViaDiscovery(arg0 context.Context, arg1 ...SubnetsResolveOption) ([]*ec2.Subnet, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResolveViaDiscovery", varargs...)
	ret0, _ := ret[0].([]*ec2.Subnet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveViaDiscovery indicates an expected call of ResolveViaDiscovery.
func (mr *MockSubnetsResolverMockRecorder) ResolveViaDiscovery(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveViaDiscovery", reflect.TypeOf((*MockSubnetsResolver)(nil).ResolveViaDiscovery), varargs...)
}

// ResolveViaNameOrIDSlice mocks base method.
func (m *MockSubnetsResolver) ResolveViaNameOrIDSlice(arg0 context.Context, arg1 []string, arg2 ...SubnetsResolveOption) ([]*ec2.Subnet, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResolveViaNameOrIDSlice", varargs...)
	ret0, _ := ret[0].([]*ec2.Subnet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResolveViaNameOrIDSlice indicates an expected call of ResolveViaNameOrIDSlice.
func (mr *MockSubnetsResolverMockRecorder) ResolveViaNameOrIDSlice(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResolveViaNameOrIDSlice", reflect.TypeOf((*MockSubnetsResolver)(nil).ResolveViaNameOrIDSlice), varargs...)
}
