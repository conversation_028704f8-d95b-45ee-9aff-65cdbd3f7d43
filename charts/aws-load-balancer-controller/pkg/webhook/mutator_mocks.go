// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/webhook (interfaces: Mutator)

// Package webhook is a generated GoMock package.
package webhook

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	runtime "k8s.io/apimachinery/pkg/runtime"
	admission "sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

// MockMutator is a mock of Mutator interface.
type MockMutator struct {
	ctrl     *gomock.Controller
	recorder *MockMutatorMockRecorder
}

// MockMutatorMockRecorder is the mock recorder for MockMutator.
type MockMutatorMockRecorder struct {
	mock *MockMutator
}

// NewMockMutator creates a new mock instance.
func NewMockMutator(ctrl *gomock.Controller) *MockMutator {
	mock := &MockMutator{ctrl: ctrl}
	mock.recorder = &MockMutatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMutator) EXPECT() *MockMutatorMockRecorder {
	return m.recorder
}

// MutateCreate mocks base method.
func (m *MockMutator) MutateCreate(arg0 context.Context, arg1 runtime.Object) (runtime.Object, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MutateCreate", arg0, arg1)
	ret0, _ := ret[0].(runtime.Object)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MutateCreate indicates an expected call of MutateCreate.
func (mr *MockMutatorMockRecorder) MutateCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MutateCreate", reflect.TypeOf((*MockMutator)(nil).MutateCreate), arg0, arg1)
}

// MutateUpdate mocks base method.
func (m *MockMutator) MutateUpdate(arg0 context.Context, arg1, arg2 runtime.Object) (runtime.Object, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MutateUpdate", arg0, arg1, arg2)
	ret0, _ := ret[0].(runtime.Object)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MutateUpdate indicates an expected call of MutateUpdate.
func (mr *MockMutatorMockRecorder) MutateUpdate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MutateUpdate", reflect.TypeOf((*MockMutator)(nil).MutateUpdate), arg0, arg1, arg2)
}

// Prototype mocks base method.
func (m *MockMutator) Prototype(arg0 admission.Request) (runtime.Object, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Prototype", arg0)
	ret0, _ := ret[0].(runtime.Object)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Prototype indicates an expected call of Prototype.
func (mr *MockMutatorMockRecorder) Prototype(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Prototype", reflect.TypeOf((*MockMutator)(nil).Prototype), arg0)
}
