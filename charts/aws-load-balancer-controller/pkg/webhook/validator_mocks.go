// Code generated by MockGen. DO NOT EDIT.
// Source: sigs.k8s.io/aws-load-balancer-controller/pkg/webhook (interfaces: Validator)

// Package webhook is a generated GoMock package.
package webhook

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	runtime "k8s.io/apimachinery/pkg/runtime"
	admission "sigs.k8s.io/controller-runtime/pkg/webhook/admission"
)

// MockValidator is a mock of Validator interface.
type MockValidator struct {
	ctrl     *gomock.Controller
	recorder *MockValidatorMockRecorder
}

// MockValidatorMockRecorder is the mock recorder for MockValidator.
type MockValidatorMockRecorder struct {
	mock *MockValidator
}

// NewMockValidator creates a new mock instance.
func NewMockValidator(ctrl *gomock.Controller) *MockValidator {
	mock := &MockValidator{ctrl: ctrl}
	mock.recorder = &MockValidatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockValidator) EXPECT() *MockValidatorMockRecorder {
	return m.recorder
}

// Prototype mocks base method.
func (m *MockValidator) Prototype(arg0 admission.Request) (runtime.Object, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Prototype", arg0)
	ret0, _ := ret[0].(runtime.Object)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Prototype indicates an expected call of Prototype.
func (mr *MockValidatorMockRecorder) Prototype(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Prototype", reflect.TypeOf((*MockValidator)(nil).Prototype), arg0)
}

// ValidateCreate mocks base method.
func (m *MockValidator) ValidateCreate(arg0 context.Context, arg1 runtime.Object) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateCreate", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateCreate indicates an expected call of ValidateCreate.
func (mr *MockValidatorMockRecorder) ValidateCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCreate", reflect.TypeOf((*MockValidator)(nil).ValidateCreate), arg0, arg1)
}

// ValidateDelete mocks base method.
func (m *MockValidator) ValidateDelete(arg0 context.Context, arg1 runtime.Object) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateDelete", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateDelete indicates an expected call of ValidateDelete.
func (mr *MockValidatorMockRecorder) ValidateDelete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateDelete", reflect.TypeOf((*MockValidator)(nil).ValidateDelete), arg0, arg1)
}

// ValidateUpdate mocks base method.
func (m *MockValidator) ValidateUpdate(arg0 context.Context, arg1, arg2 runtime.Object) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateUpdate", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateUpdate indicates an expected call of ValidateUpdate.
func (mr *MockValidatorMockRecorder) ValidateUpdate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUpdate", reflect.TypeOf((*MockValidator)(nil).ValidateUpdate), arg0, arg1, arg2)
}
