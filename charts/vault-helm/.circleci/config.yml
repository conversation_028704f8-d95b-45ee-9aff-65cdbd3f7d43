version: 2.1
orbs:
  slack: circleci/slack@3.4.2

jobs:
  bats-unit-test:
    docker:
      # This image is built from test/docker/Test.dockerfile
      - image: docker.mirror.hashicorp.services/hashicorpdev/vault-helm-test:0.2.0
    steps:
      - checkout
      - run: bats ./test/unit -t

  chart-verifier:
    docker:
      - image: docker.mirror.hashicorp.services/cimg/go:1.16
    environment:
      BATS_VERSION: "1.3.0"
      CHART_VERIFIER_VERSION: "1.2.1"
    steps:
      - checkout
      - run:
          name: install chart-verifier
          command: go get github.com/redhat-certification/chart-verifier@${CHART_VERIFIER_VERSION}
      - run:
          name: install bats
          command: |
            curl -sSL https://github.com/bats-core/bats-core/archive/v${BATS_VERSION}.tar.gz -o /tmp/bats.tgz
            tar -zxf /tmp/bats.tgz -C /tmp
            sudo /bin/bash /tmp/bats-core-${BATS_VERSION}/install.sh /usr/local
      - run:
          name: run chart-verifier tests
          command: bats ./test/chart -t

  acceptance:
    docker:
      # This image is build from test/docker/Test.dockerfile
      - image: docker.mirror.hashicorp.services/hashicorpdev/vault-helm-test:0.2.0

    steps:
      - checkout
      - run:
          name: terraform init & apply
          command: |
            echo -e "${GOOGLE_APP_CREDS}" | base64 -d > vault-helm-test.json
            export GOOGLE_CREDENTIALS=vault-helm-test.json
            make provision-cluster
      - run:
          name: Run acceptance tests
          command: bats ./test/acceptance -t

      - run:
          name: terraform destroy
          command: |
            export GOOGLE_CREDENTIALS=vault-helm-test.json
            make destroy-cluster
          when: always
  update-helm-charts-index:
    docker:
      - image: docker.mirror.hashicorp.services/circleci/golang:1.15.3
    steps:
      - checkout
      - run:
          name: verify Chart version matches tag version
          command: |
            GO111MODULE=on go get github.com/mikefarah/yq/v2
            git_tag=$(echo "${CIRCLE_TAG#v}")
            chart_tag=$(yq r Chart.yaml version)
            if [ "${git_tag}" != "${chart_tag}" ]; then
              echo "chart version (${chart_tag}) did not match git version (${git_tag})"
              exit 1
            fi
      - run:
          name: update helm-charts index
          command: |
            curl --show-error --silent --fail --user "${CIRCLE_TOKEN}:" \
                -X POST \
                -H 'Content-Type: application/json' \
                -H 'Accept: application/json' \
                -d "{\"branch\": \"main\",\"parameters\":{\"SOURCE_REPO\": \"${CIRCLE_PROJECT_USERNAME}/${CIRCLE_PROJECT_REPONAME}\",\"SOURCE_TAG\": \"${CIRCLE_TAG}\"}}" \
                "${CIRCLE_ENDPOINT}/${CIRCLE_PROJECT}/pipeline"
      - slack/status:
          fail_only: true
          failure_message: "Failed to trigger an update to the helm charts index. Check the logs at: ${CIRCLE_BUILD_URL}"

workflows:
  version: 2
  # Note: unit and acceptance tests are now being run in GitHub Actions
  update-helm-charts-index:
    jobs:
      - update-helm-charts-index:
          context: helm-charts-trigger-vault
          filters:
            tags:
              only: /^v.*/
            branches:
              ignore: /.*/
