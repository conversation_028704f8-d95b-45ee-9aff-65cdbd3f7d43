name: Acceptance Tests

on:
  push:
    branches:
      - main
  workflow_dispatch: {}

jobs:
  kind:
    strategy:
      fail-fast: false
      matrix:
        kind-k8s-version: [1.16.15, 1.20.15, 1.21.12, 1.22.9, 1.23.6, 1.24.1]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup test tools
        uses: ./.github/workflows/setup-test-tools

      - name: Create K8s Kind Cluster
        uses: helm/kind-action@v1.2.0
        with:
          config: test/kind/config.yaml
          node_image: kindest/node:v${{ matrix.kind-k8s-version }}
          version: v0.14.0

      - run: bats ./test/acceptance -t
        env:
          VAULT_LICENSE_CI: ${{ secrets.VAULT_LICENSE_CI }}
