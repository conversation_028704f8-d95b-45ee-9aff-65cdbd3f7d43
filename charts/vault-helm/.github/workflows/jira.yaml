on:
  issues:
    types: [opened, closed, deleted, reopened]
  pull_request_target:
    types: [opened, closed, reopened]
  issue_comment: # Also triggers when commenting on a PR from the conversation view
    types: [created]

name: Jira Sync

jobs:
  sync:
    runs-on: ubuntu-latest
    name: Jira sync
    steps:
    - name: <PERSON><PERSON>
      uses: atlassian/g<PERSON><PERSON>-login@v2.0.0
      env:
        JIRA_BASE_URL: ${{ secrets.JIRA_SYNC_BASE_URL }}
        JIRA_USER_EMAIL: ${{ secrets.JIRA_SYNC_USER_EMAIL }}
        JIRA_API_TOKEN: ${{ secrets.JIRA_SYNC_API_TOKEN }}

    - name: Preprocess
      if: github.event.action == 'opened' || github.event.action == 'created'
      id: preprocess
      run: |
        if [[ "${{ github.event_name }}" == "pull_request_target" ]]; then
          echo "::set-output name=type::PR"
        else
          echo "::set-output name=type::ISS"
        fi

    - name: Create ticket
      if: github.event.action == 'opened'
      uses: tomhjp/gh-action-jira-create@v0.2.0
      with:
        project: VAULT
        issuetype: "GH Issue"
        summary: "${{ github.event.repository.name }} [${{ steps.preprocess.outputs.type }} #${{ github.event.issue.number || github.event.pull_request.number }}]: ${{ github.event.issue.title || github.event.pull_request.title }}"
        description: "${{ github.event.issue.body || github.event.pull_request.body }}\n\n_Created from GitHub Action for ${{ github.event.issue.html_url || github.event.pull_request.html_url }} from ${{ github.actor }}_"
        # customfield_10089 is Issue Link custom field
        # customfield_10091 is team custom field
        extraFields: '{"fixVersions": [{"name": "TBD"}], "customfield_10091": ["ecosystem", "runtime"], "customfield_10089": "${{ github.event.issue.html_url || github.event.pull_request.html_url }}"}'

    - name: Search
      if: github.event.action != 'opened'
      id: search
      uses: tomhjp/gh-action-jira-search@v0.2.1
      with:
        # cf[10089] is Issue Link custom field
        jql: 'project = "VAULT" and cf[10089]="${{ github.event.issue.html_url || github.event.pull_request.html_url }}"'

    - name: Sync comment
      if: github.event.action == 'created' && steps.search.outputs.issue
      uses: tomhjp/gh-action-jira-comment@v0.2.0
      with:
        issue: ${{ steps.search.outputs.issue }}
        comment: "${{ github.actor }} ${{ github.event.review.state || 'commented' }}:\n\n${{ github.event.comment.body || github.event.review.body }}\n\n${{ github.event.comment.html_url || github.event.review.html_url }}"

    - name: Close ticket
      if: (github.event.action == 'closed' || github.event.action == 'deleted') && steps.search.outputs.issue
      uses: atlassian/gajira-transition@v2.0.1
      with:
        issue: ${{ steps.search.outputs.issue }}
        transition: Close

    - name: Reopen ticket
      if: github.event.action == 'reopened' && steps.search.outputs.issue
      uses: atlassian/gajira-transition@v2.0.1
      with:
        issue: ${{ steps.search.outputs.issue }}
        transition: "Pending Triage"
