name: Setup common testing tools
description: Install bats and python-yq

runs:
  using: "composite"
  steps:
    - uses: actions/setup-node@v2
      with:
        node-version: '14'
    - run: npm install -g bats@${BATS_VERSION}
      shell: bash
      env:
        BATS_VERSION: '1.5.0'
    - run: bats -v
      shell: bash
    - uses: actions/setup-python@v2
    - run: pip install yq
      shell: bash
