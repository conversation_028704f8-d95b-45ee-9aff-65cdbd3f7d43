server:
  standalone:
    enabled: false"

  extraEnvironmentVars:
    AWS_ROLE_ARN: "arn:aws:iam::************:role/kubeiam-vault"
    AWS_ROLE_SESSION_NAME: vault
    AWS_WEB_IDENTITY_TOKEN_FILE: "/var/run/secrets/eks.amazonaws.com/serviceaccount/token"


  statefulSet:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-vault

  serviceAccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-vault

  service:
    enabled: true
    # clusterIP controls whether a Cluster IP address is attached to the
    # Vault service within Kubernetes.  By default the Vault service will
    # be given a Cluster IP address, set to None to disable.  When disabled
    # Kubernetes will create a "headless" service.  Headless services can be
    # used to communicate with pods directly through DNS instead of a round robin
    # load balancer.
    # clusterIP: None

    # Configures the service type for the main Vault service.  Can be ClusterIP
    # or NodePort.
    type: ClusterIP
  ingress:
    enabled: false
    # labels: {}
      # traffic: external
    annotations: 
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/target-type: ip
      kubernetes.io/ingress.class: alb
      # |
      # kubernetes.io/ingress.class: nginx
      # kubernetes.io/tls-acme: "true"
      #   or
      # kubernetes.io/ingress.class: nginx
      # kubernetes.io/tls-acme: "true"

    # Optionally use ingressClassName instead of deprecated annotation.
    # See: https://kubernetes.io/docs/concepts/services-networking/ingress/#deprecated-annotation
    ingressClassName: "alb"

    # As of Kubernetes 1.19, all Ingress Paths must have a pathType configured. The default value below should be sufficient in most cases.
    # See: https://kubernetes.io/docs/concepts/services-networking/ingress/#path-types for other possible values.
    # pathType: Prefix

    # When HA mode is enabled and K8s service registration is being used,
    # configure the ingress to point to the Vault active service.
    # activeService: true
    hosts:
      - host: vault.infra.fingermark.tech
        paths: []
    ## Extra paths to prepend to the host configuration. This is useful when working with annotation based services.
    # extraPaths: []
    # - path: /*
    #   backend:
    #     service:
    #       name: ssl-redirect
    #       port:
    #         number: use-annotation
    # tls: []

  ha:
    enabled: true
    replicas: 5

    raft:

      # Enables Raft integrated storage
      enabled: true
      config: |
        ui = true

        listener "tcp" {
          tls_disable = 1
          address = "[::]:8200"
          cluster_address = "[::]:8201"
          # tls_cert_file = "/etc/ssl/vaultssl/vault-cert.pem"
          # tls_key_file = "/etc/ssl/vaultssl/vault-key.pem"
          # tls_disable_client_certs = "true"
          telemetry {
            unauthenticated_metrics_access = true
          }
        }
        telemetry "prometheus" {
          prometheus_retention_time = "30s"
          disable_hostname = true
        }

        storage "raft" {
          path = "/vault/data"

          retry_join {
            leader_api_addr = "http://vault-0.vault-internal:8200"
          }
          retry_join {
            leader_api_addr = "http://vault-1.vault-internal:8200"
          }
          retry_join {
            leader_api_addr = "http://vault-2.vault-internal:8200"
          }
          retry_join {
            leader_api_addr = "http://vault-3.vault-internal:8200"
          }
          retry_join {
            leader_api_addr = "http://vault-4.vault-internal:8200"
          }
        }

        sealt

        
        service_registration "kubernetes" {}

  annotations:
    prometheus.io/scrapevault: 'true'
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-vault
    prometheus.io/port: '8200'
    prometheus.io/path: '/v1/sys/metrics'

  affinity: |
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              app.kubernetes.io/name: {{ template "vault.name" . }}
              app.kubernetes.io/instance: "{{ .Release.Name }}"
              component: server
          topologyKey: kubernetes.io/hostname




# Vault UI
ui:
  # True if you want to create a Service entry for the Vault UI.
  #
  # serviceType can be used to control the type of service created. For
  # example, setting this to "LoadBalancer" will create an external load
  # balancer (for supported K8S installations) to access the UI.
  enabled: true
  publishNotReadyAddresses: true
  # The service should only contain selectors for active Vault pod
  activeVaultPodOnly: false
  serviceType: "ClusterIP"
  serviceNodePort: null
  externalPort: 8200
  targetPort: 8200

  # The externalTrafficPolicy can be set to either Cluster or Local
  # and is only valid for LoadBalancer and NodePort service types.
  # The default value is Cluster.
  # ref: https://kubernetes.io/docs/concepts/services-networking/service/#external-traffic-policy
  externalTrafficPolicy: Cluster

  # loadBalancerSourceRanges:
  #   - **************/32

  # loadBalancerIP:

  # Extra annotations to attach to the ui service
  # This can either be YAML or a YAML-formatted multi-line templated string map
  # of the annotations to apply to the ui service
  annotations: {}
