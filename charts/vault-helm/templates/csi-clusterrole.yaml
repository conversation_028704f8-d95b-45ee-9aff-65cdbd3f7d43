{{- template "vault.csiEnabled" . -}}
{{- if .csiEnabled -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "vault.fullname" . }}-csi-provider-clusterrole
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-csi-provider
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
rules:
- apiGroups:
  - ""
  resources:
  - serviceaccounts/token
  verbs:
  - create
{{- end }}
