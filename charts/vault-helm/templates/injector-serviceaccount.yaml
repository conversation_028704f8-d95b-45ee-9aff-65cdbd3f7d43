{{- template "vault.injectorEnabled" . -}}
{{- if .injectorEnabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "vault.fullname" . }}-agent-injector
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-agent-injector
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
{{ end }}
