{{ template "vault.mode" . }}
{{- if ne .mode "external" }}
{{- if .serverEnabled -}}
{{- if ne .mode "dev" -}}
{{ if or (.Values.server.standalone.config) (.Values.server.ha.config) -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "vault.fullname" . }}-config
  namespace: {{ .Release.Namespace }}
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  extraconfig-from-values.hcl: |-
  {{- if or (eq .mode "ha") (eq .mode "standalone") }}
  {{- $type := typeOf (index .Values.server .mode).config }}
  {{- if eq $type "string" }}
    disable_mlock = true
  {{- if eq .mode "standalone" }}
    {{ tpl .Values.server.standalone.config . | nindent 4 | trim }}
  {{- else if and (eq .mode "ha") (eq (.Values.server.ha.raft.enabled | toString) "false") }}
    {{ tpl .Values.server.ha.config . | nindent 4 | trim }}
  {{- else if and (eq .mode "ha") (eq (.Values.server.ha.raft.enabled | toString) "true") }}
    {{ tpl .Values.server.ha.raft.config . | nindent 4 | trim }}
  {{ end }}
  {{- else }}
  {{- if and (eq .mode "ha") (eq (.Values.server.ha.raft.enabled | toString) "true") }}
{{ merge (dict "disable_mlock" true) (index .Values.server .mode).raft.config | toPrettyJson | indent 4 }}
  {{- else }}
{{ merge (dict "disable_mlock" true) (index .Values.server .mode).config | toPrettyJson | indent 4 }}
  {{- end }}
  {{- end }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
