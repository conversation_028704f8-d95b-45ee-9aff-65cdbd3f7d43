{{ template "vault.mode" . }}
{{- if ne .mode "external" -}}
{{- if .serverEnabled -}}
{{- if and (eq .mode "ha") (eq (.Values.server.ha.disruptionBudget.enabled | toString) "true") -}}
# PodDisruptionBudget to prevent degrading the server cluster through
# voluntary cluster changes.
apiVersion: {{ ge .Capabilities.KubeVersion.Minor "21" | ternary "policy/v1" "policy/v1beta1" }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "vault.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  maxUnavailable: {{ template "vault.pdb.maxUnavailable" . }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "vault.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      component: server
{{- end -}}
{{- end -}}
{{- end -}}
