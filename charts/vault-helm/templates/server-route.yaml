{{- if .Values.global.openshift }}
{{- if ne .mode "external" }}
{{- if .Values.server.route.enabled -}}
{{- $serviceName := include "vault.fullname" . -}}
{{- if and (eq .mode "ha" ) (eq (.Values.server.route.activeService | toString) "true") }}
{{- $serviceName = printf "%s-%s" $serviceName "active" -}}
{{- end }}
kind: Route
apiVersion: route.openshift.io/v1
metadata:
  name: {{ template "vault.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- with .Values.server.route.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- template "vault.route.annotations" . }}
spec:
  host: {{ .Values.server.route.host }}
  to:
    kind: Service
    name: {{ $serviceName }}
    weight: 100
  port:
    targetPort: 8200
  tls:
    {{- toYaml .Values.server.route.tls | nindent  4 }}
{{- end }}
{{- end }}
{{- end }}
