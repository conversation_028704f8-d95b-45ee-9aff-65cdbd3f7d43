{{ template "vault.serverServiceAccountEnabled" . }}
{{- if .serverServiceAccountEnabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "vault.serviceAccount.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    helm.sh/chart: {{ include "vault.chart" . }}
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  {{ template "vault.serviceAccount.annotations" . }}
{{ end }}
