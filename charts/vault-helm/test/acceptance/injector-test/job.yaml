---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: pgdump
  labels:
    app: pgdump
---
apiVersion: batch/v1
kind: Job
metadata:
  name: pgdump
spec:
  backoffLimit: 0
  template:
    metadata:
      name: pgdump
      labels:
        app: pgdump
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-inject-secret-db-creds: "database/creds/db-backup"
        vault.hashicorp.com/agent-inject-template-db-creds: |
          {{- with secret "database/creds/db-backup" -}}
          postgresql://{{ .Data.username }}:{{ .Data.password }}@postgres.acceptance.svc.cluster.local:5432/mydb
          {{- end }}
        vault.hashicorp.com/role: "db-backup"
        vault.hashicorp.com/agent-pre-populate-only: "true"
    spec:
      serviceAccountName: pgdump
      containers:
      - name: pgdump
        image: postgres:11.5
        command:
          - "/bin/sh"
          - "-ec"
        args:
          - "/usr/bin/pg_dump $(cat /vault/secrets/db-creds) --no-owner > /dev/stdout"
      restartPolicy: Never
