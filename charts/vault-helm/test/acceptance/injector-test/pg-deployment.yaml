---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
   - port: 5432
     targetPort: 5432
  selector:
   app: postgres
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        service: postgres
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:11.5
          ports:
            - containerPort: 5432
          env:
            - name: POSTGRES_DB
              value: mydb 
            - name: POSTGRES_USER
              value: postgres
            - name: POSTGRES_PASSWORD
              value: password
          volumeMounts:
            - mountPath: "/var/lib/postgresql"
              name: "pgdata"
            - mountPath: "/docker-entrypoint-initdb.d"
              name: "pgconf"
      volumes:
        - name: pgdata
          emptyDir: {}
        - name: pgconf
          configMap: 
            name: "pg-init"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pg-init
  labels:
    app: postgres
data:
  setup.sql: |
    CREATE ROLE vault;
    ALTER ROLE vault WITH SUPERUSER LOGIN PASSWORD 'vault';
    
    \c mydb 
    CREATE SCHEMA app;
    CREATE TABLE app.inventory(id int);
    INSERT INTO app.inventory(id) VALUES (0);
