---
apiVersion: source.toolkit.fluxcd.io/v1beta2
kind: GitRepository
metadata:
  name: atlantis
  namespace: atlantis
spec:
  interval: 1m0s
  ref:
    branch: master
  secretRef:
    name: flux-system
  url: ssh://*****************/fingermarkltd/infra-k8s.git
  ignore: |
    # exclude all
    /*
    # include charts directory
    !/charts/atlantis
---
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: atlantis
  namespace: atlantis
  labels:
    app.kubernetes.io/component: atlantis
    app.kubernetes.io/managed-by: helm
spec:
  interval: 5m
  chart:
    spec:
      chart: ./charts/atlantis/helm-charts/charts/atlantis
      version: "4.10.2"
      sourceRef:
        kind: GitRepository
        name: atlantis
        namespace: atlantis
      interval: 2m
  values:
    statefulset:
      annotations:
        vault.hashicorp.com/agent-inject: 'true'
        vault.hashicorp.com/role: 'atlantis'
        vault.hashicorp.com/agent-pre-populate-only : "true"
        vault.hashicorp.com/agent-inject-secret-config: 'eks/data/atlantis'
        vault.hashicorp.com/agent-inject-template-config: |
          {{- with secret "eks/data/atlantis" -}}
            export ATLANTIS_BITBUCKET_TOKEN="{{ .Data.data.eyecue_deployer_bitbucket_token }}"
            export VAULT_ADDR="https://central.infra.fingermark.tech/vault"
            export VAULT_TOKEN="{{ .Data.data.vault_token }}"
            export SSH_BITBUCKET_PRIVATE_KEY='{{ .Data.data.eyecue_deployer_ssh_private_key }}'
            export ATLANTIS_SLACK_TOKEN='{{ .Data.data.slack_token }}'
          {{- end }}
        vault.hashicorp.com/agent-inject-secret-supersonicinfra.json: 'secret/terraform/newrelic/supersonic/supersonic-infra-svc-creds'
        vault.hashicorp.com/agent-inject-template-supersonicinfra.json: |
          {{- with secret "secret/terraform/newrelic/supersonic/supersonic-infra-svc-creds" -}}
            {{ .Data.data.credentials }}
          {{- end }}
        vault.hashicorp.com/agent-inject-secret-supersonicdev.json: 'secret/terraform/newrelic/supersonic/supersonic-dev-svc-creds'
        vault.hashicorp.com/agent-inject-template-supersonicdev.json: |
          {{- with secret "secret/terraform/newrelic/supersonic/supersonic-dev-svc-creds" -}}
            {{ .Data.data.credentials }}
          {{- end }}
        vault.hashicorp.com/agent-inject-secret-supersonicuat.json: 'secret/terraform/newrelic/supersonic/supersonic-uat-svc-creds'
        vault.hashicorp.com/agent-inject-template-supersonicuat.json: |
          {{- with secret "secret/terraform/newrelic/supersonic/supersonic-uat-svc-creds" -}}
            {{ .Data.data.credentials }}
          {{- end }}
        vault.hashicorp.com/agent-inject-secret-supersonicprod.json: 'secret/terraform/newrelic/supersonic/supersonic-prod-svc-creds'
        vault.hashicorp.com/agent-inject-template-supersonicprod.json: |
          {{- with secret "secret/terraform/newrelic/supersonic/supersonic-prod-svc-creds" -}}
            {{ .Data.data.credentials }}
          {{- end }}
    image:
      repository: ************.dkr.ecr.ap-southeast-2.amazonaws.com/atlantis
      tag: "master-48-734bdfa72f5482ed868b896d7e5aa8c121e4183e"
      pullPolicy: Always
    # Dont keep any spaces in the orgAllowlist or else it doesn't work
    orgAllowlist: "bitbucket.org/fingermarkltd/*"
    logLevel: "debug"
    replicaCount: 1
    disableRepoLocking: false
    serviceAccount:
      create: false
      mount: true
      name: "atlantis"
    defaultTFVersion: 1.7.2
    bitbucket:
      user: "eyecue-deployer"
    volumeClaim:
      enabled: true
      # Disk space for to check out repositories
      dataStorage: 50Gi
    service:
      type: ClusterIP
      port: 4141
      targetPort: 4141
      loadBalancerIP: null
    atlantisUrl: "https://atlantis.infra.fingermark.tech"
    # extraArgs:
    #   - --autoplan-file-list=["*.tf", "../modules/**/*.tf"]
    #   - --autoplan-modules=true
    resources:
      requests:
        memory: 1.0Gi
        cpu: 512m
      limits:
        memory: 2.0Gi
        cpu: 1028m
    repoConfig: |
      ---
      # repos lists the config for repos.
      repos:
        # id can either be an exact repo ID or a regex.
        # If using a regex, it must start and end with a slash.
        # Repo ID's are of the form {VCS hostname}/{org}/{repo name}, ex.
        # github.com/runatlantis/atlantis.
        - id: /.*/
          # branch is an regex matching pull requests by base branch
          # (the branch the pull request is getting merged into).
          # By default, all branches are matched
          branch: /master/

          # apply_requirements sets the Apply Requirements for all repos that match.
          apply_requirements: [approved, mergeable]

          # allowed_overrides specifies which keys can be overridden by this repo in
          # its atlantis.yaml file.
          allowed_overrides:
            [delete_source_branch_on_merge, repo_locking]

          # allow_custom_workflows defines whether this repo can define its own
          # workflows. If false (default), the repo can only use server-side defined
          # workflows.
          # allow_custom_workflows: true
          workflow: default

          # repo_locking defines whether lock repository when planning.
          # If true (default), atlantis try to get a lock.
          # repo_locking: true
      workflows:
        default:
          plan:
            steps:
              - run: terraform fmt -check=true -diff=true
              - run: terraform init --upgrade
              - env:
                  name: TF_CLI_ARGS
                  command: echo $COMMENT_ARGS | tr ',' ' ' | sed -r 's/\\(.)/\1/g'
              - run: terraform plan -out $PLANFILE > output.txt && sed -n '/Terraform/,$p' output.txt | cat && rm output.txt
          apply:
            steps:
              - env:
                  name: TF_CLI_ARGS
                  command: echo $COMMENT_ARGS | tr ',' ' ' | sed -r 's/\\(.)/\1/g'
              - run: terraform apply $PLANFILE
---

