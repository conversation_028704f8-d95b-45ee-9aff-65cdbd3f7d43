---
apiVersion: awx.ansible.com/v1beta1
kind: AWX
metadata:
  name: fingermark-awx
  namespace: awx
  labels:
    app.kubernetes.io/component: awx
    app.kubernetes.io/managed-by: awx-operator
    app.kubernetes.io/name: fingermark-awx
    app.kubernetes.io/operator-version: 1.0.0
    app.kubernetes.io/part-of: fingermark-awx
spec:
  service_type: ClusterIP
  admin_user: admin
  create_preload_data: true
  garbage_collect_secrets: true
  image_pull_policy: IfNotPresent
  loadbalancer_port: 80
  loadbalancer_protocol: http
  nodeport_port: 30080
  projects_persistence: false
  projects_storage_access_mode: ReadWriteMany
  projects_storage_size: 8Gi
  replicas: 1
  route_tls_termination_mechanism: Edge
  task_privileged: false
  image: quay.io/ansible/awx
  image_version: 21.8.0
  ee_images:
  - name: awx-ee
    image: quay.io/ansible/awx-ee:latest