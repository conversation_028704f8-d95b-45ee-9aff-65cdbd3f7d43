---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dynamodbs3-script
  namespace: cronjobs
data:
  dynamodb_s3export.py: |
    import boto3
    import os
    import time
    import logging
    from datetime import datetime

    # Setting Vars
    dynamodb_region = os.environ['DYNAMODB_REGION']
    source_bucket_name = os.environ['S3_SOURCE_BUCKET']
    role_arn = os.environ["ASSUME_ROLE_ARN"]
    table_arn=os.environ["DYNAMODB_TABLE_ARN"]
    destination_bucket_name = os.environ["S3_DESTINATION_BUCKET"]
    destination_folder_path = os.environ.get("S3_DESTINATION_BUCKET_INNER_PATH", "")
    export_name=os.environ.get("EXPORT_NAME", datetime.now().strftime("%Y%m%d-%Hh%Mm"))

    logging.basicConfig(format='%(asctime)s %(levelname)s:%(message)s', level=logging.INFO, datefmt='%d/%m/%Y %I:%M:%S')
    logging.info("******************************")
    logging.info("DynamoDB Table Export to S3 Script")
    logging.info("******************************")


    logging.info("Initiating client")
    # Assuming AWS Role
    sts_client = boto3.client("sts")
    assumed_role = sts_client.assume_role(
        RoleArn=role_arn,
        RoleSessionName="AssumedRoleSession"
    )

    # Set the Dynamodb resource using the credentials from the assumed role
    dynamodb = boto3.client(
        'dynamodb', 
        region_name=dynamodb_region,
        aws_access_key_id=assumed_role["Credentials"]["AccessKeyId"],
        aws_secret_access_key=assumed_role["Credentials"]["SecretAccessKey"],
        aws_session_token=assumed_role["Credentials"]["SessionToken"]
    )

    # Set the S3 resource using the credentials from the assumed role
    s3_resource = boto3.resource(
        "s3",
        aws_access_key_id=assumed_role["Credentials"]["AccessKeyId"],
        aws_secret_access_key=assumed_role["Credentials"]["SecretAccessKey"],
        aws_session_token=assumed_role["Credentials"]["SessionToken"]
    )
    destination_bucket = s3_resource.Bucket(destination_bucket_name)

    # Dynamodb Export to S3 Bucket
    export_details = dynamodb.export_table_to_point_in_time(
            TableArn=table_arn,
            S3Bucket=source_bucket_name,
            ExportFormat='DYNAMODB_JSON',
            S3Prefix=export_name
        )
    export_arn = export_details['ExportDescription']['ExportArn']

    # Verifying the table export
    while True:
        export_details = dynamodb.describe_export(ExportArn=export_arn)['ExportDescription']
        status = export_details['ExportStatus']
        if status in ['CREATING', 'IN_PROGRESS','DELETING']:
            logging.info("Export for %s is %s. Waiting for completion...",table_arn,status)
            time.sleep(10)
        elif status == 'COMPLETED':
            logging.info("Export %s has been %s.",table_arn,status)
            logging.info("Export for %s has been stored at %s.",table_arn,export_details['S3Prefix'])
            break
        else:
            logging.info("Unexpected backup status: %s",status)
            break

    # Uploading export to S3 Bucket
    for obj in s3_resource.Bucket(source_bucket_name).objects.filter(Prefix=export_name):
        destination_key = destination_folder_path + "/" + export_name + obj.key[len(export_name):]
        try:
            destination_bucket.Object(destination_key).copy_from(
                CopySource={'Bucket': source_bucket_name, 'Key': obj.key}
            )
        except Exception as e:
            logging.error("Error in copying %s:  %s",obj.key,e)
        else:
            logging.info("Replication successful for %s",obj.key)

  requirements.txt: |
    boto3~=1.26

---

apiVersion: v1
kind: ServiceAccount
metadata:
  name: dynamos3export
  namespace: cronjobs
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-dynamos3export-cvprod2dataprod
---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: dynamodbs3-replication
  namespace: cronjobs
spec:
  schedule: "0 17 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          volumes:
            - name: script
              configMap:
                name: dynamodbs3-script
          serviceAccountName: dynamos3export
          restartPolicy: OnFailure
          containers:
          - name: s3-replication
            image: python:3.10-bullseye
            imagePullPolicy: Always
            command: [ "/bin/sh","-c","--"]
            args: ["pip3 install -r /scripts/requirements.txt && python3 /scripts/dynamodb_s3export.py" ]
            volumeMounts:
              - name: script
                mountPath: /scripts
            env:
              - name: ASSUME_ROLE_ARN
                value: "arn:aws:iam::************:role/infra-dynamodbs3-export-role"
              - name: S3_SOURCE_BUCKET
                value: "cv-prod-dynamodb-exports"
              - name: S3_DESTINATION_BUCKET
                value: "fm-prod-adhoc-datalake-ap-southeast-2"
              - name: S3_DESTINATION_BUCKET_INNER_PATH
                value: "eyecue/chromebox-action-log-data"
              - name: DYNAMODB_REGION
                value: "ap-southeast-2"
              - name: DYNAMODB_TABLE_ARN
                value: "arn:aws:dynamodb:ap-southeast-2:************:table/chromebox-action-log"
