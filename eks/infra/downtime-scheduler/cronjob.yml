apiVersion: v1
kind: ServiceAccount
metadata:
  name: infra-downtime-scheduler
  namespace: cronjobs
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-downtime-scheduler

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: infra-downtime-scheduler
  labels:
    app: infra-downtime-scheduler
  namespace: cronjobs
spec:
  schedule: "0 11 * * *"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: infra-downtime-scheduler
          annotations:
            eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-downtime-scheduler
            vault.hashicorp.com/agent-inject: "true"
            vault.hashicorp.com/agent-init-first: "true"
            vault.hashicorp.com/agent-pre-populate-only : "true"
            vault.hashicorp.com/auth-path: "auth/kubernetes"
            vault.hashicorp.com/role: "infra-downtime-scheduler-role"
            vault.hashicorp.com/agent-inject-secret-config: "eks/data/infra-downtime-scheduler"
            vault.hashicorp.com/agent-inject-template-config: |
              {{ with secret "eks/data/infra-downtime-scheduler" -}}
              export ICINGA_PASSWORD="{{ .Data.data.ICINGA_PASSWORD }}"
              {{- end }}
        spec:
          serviceAccountName: infra-downtime-scheduler
          containers:
          - name: infra-downtime-scheduler
            image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/infra-downtime-scheduler:latest
            imagePullPolicy: Always
            ports:
            - containerPort: 80
            envFrom:
            - configMapRef:
                name: infra-downtime-scheduler-config
            command: 
              ["/bin/bash", "-c"]
            args: 
              ["source /vault/secrets/config && python main.py"]
          restartPolicy: OnFailure

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: infra-downtime-scheduler-config
  namespace: cronjobs
data:
  ICINGA_USERNAME: "root"
  ICINGA_URL_HOSTS: "https://*************:5665/v1/objects/hosts"
  ICINGA_URL_DOWNTIME: "https://*************:5665/v1/actions/schedule-downtime"
  TABLE_NAME: "eyecue-dashboard-stores"
  ROLE_ARN: arn:aws:iam::************:role/infra-downtime-scheduler-role
  REGION_NAME: "ap-southeast-2"
