apiVersion: v1
kind: Service
metadata:
  name: infra-api
  namespace: infra-api
spec:
  selector:
    app.kubernetes.io/name: infra-api
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: infra-api
  namespace: infra-api
  labels:
    app: infra-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: infra-api
      app: infra-api
  template:
    metadata:
      name: infra-api
      labels:
        app: infra-api
        app.kubernetes.io/name: infra-api
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-infra-api
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-init-first: "true"
        vault.hashicorp.com/auth-path: "auth/kubernetes"
        vault.hashicorp.com/role: "infra-api"
        vault.hashicorp.com/agent-inject-secret-config: "eks/data/infra-api"
        vault.hashicorp.com/agent-inject-template-config: |
          {{ with secret "eks/data/infra-api" -}}
            export AWS_ACCESS_KEY="{{ .Data.data.AWS_ACCESS_KEY }}"
            export AWS_SECRET_ACCESS_KEY="{{ .Data.data.AWS_SECRET_ACCESS_KEY }}"
          {{- end }}
    spec:
      serviceAccountName: infra-api
      containers:
        - name: infra-api
          image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/infra-api:master-0e6de59 # {"$imagepolicy": "flux-system:infra-api"}
          imagePullPolicy: Always
          command: ["/bin/bash"]
          args: ["-c", "uvicorn main:app --proxy-headers --host 0.0.0.0 --port 8000"]
          ports:
            - name: infra-api
              containerPort: 8000
          resources:
            requests:
              memory: "200Mi"
              cpu: "200m"
            limits:
              memory: "512Mi"
              cpu: "512m"
          livenessProbe:
            httpGet:
              path: /health
              port: infra-api
            initialDelaySeconds: 10
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: infra-api
            initialDelaySeconds: 10
            periodSeconds: 10
