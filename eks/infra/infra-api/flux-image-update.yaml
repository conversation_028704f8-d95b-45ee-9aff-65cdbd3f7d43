---
apiVersion: image.toolkit.fluxcd.io/v1beta1
kind: ImageRepository
metadata:
  name: infra-api
  namespace: flux-system
spec:
  image: 055313672806.dkr.ecr.ap-southeast-2.amazonaws.com/infra-api
  interval: 20m0s
---
apiVersion: image.toolkit.fluxcd.io/v1beta1
kind: ImagePolicy
metadata:
  name: infra-api
  namespace: flux-system
spec:
  imageRepositoryRef:
    name: infra-api
  filterTags:
    pattern: '^master-\w+$'
  policy:
    semver:
      range: ">=0.0.0"
---
apiVersion: kustomize.toolkit.fluxcd.io/v1beta1
kind: Kustomization
metadata:
  name: infra-api
  namespace: flux-system
spec:
  interval: 20m0s
  path: "./deployments/infra-api"
  prune: true
  sourceRef:
    kind: GitRepository
    name: infra-k8s
  images:
    - name: 055313672806.dkr.ecr.ap-southeast-2.amazonaws.com/infra-api
      newTag: master-4osldhbf