kind: Service
apiVersion: v1
metadata:
  name: awx
  namespace: ingress-nginx
spec:
  type: ExternalName
  externalName: fingermark-awx-service.awx.svc.cluster.local
  ports:
    - port: 80
      protocol: TCP
---
kind: Service
apiVersion: v1
metadata:
  name: vault
  namespace: ingress-nginx
spec:
  type: ExternalName
  externalName: vault-active.vault.svc.cluster.local
  ports:
    - port: 8200
      protocol: TCP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/configuration-snippet: |
      rewrite ^/awx$ https://awx.infra.fingermark.tech/$2 permanent;
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.class: nginx
  name: ingress-connect-nginx
  namespace: ingress-nginx
spec:
  rules:
    - host: central.infra.fingermark.tech
      http:
        paths:
          - backend:
              service:
                name: awx
                port:
                  number: 80
            path: /awx(/|$)(.*)
            pathType: Prefix
          - backend:
              service:
                name: vault
                port:
                  number: 8200
            path: /vault(/|$)(.*)
            pathType: Prefix
---
