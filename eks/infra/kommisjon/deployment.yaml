apiVersion: apps/v1
kind: Deployment
metadata:
  name: kommisjon-slack-bot
  labels:
    app: kommisjon-slack-bot
  namespace: kommisjon
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kommisjon-slack-bot
  template:
    metadata:
      labels:
        app: kommisjon-slack-bot
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/kubeiam-kommisjon
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-init-first: "true"
        vault.hashicorp.com/auth-path: "auth/kubernetes"
        vault.hashicorp.com/role: "kommisjon"
        vault.hashicorp.com/agent-inject-secret-config: "eks/data/kommisjon"
        vault.hashicorp.com/agent-inject-template-config: |
          {{ with secret "eks/data/kommisjon" -}}
            export SLACK_USER_TOKEN="{{ .Data.data.SLACK_USER_TOKEN }}"
            export SLACK_BOT_TOKEN="{{ .Data.data.SLACK_BOT_TOKEN }}"
            export SLACK_SIGNING_SECRET="{{ .Data.data.SLACK_SIGNING_SECRET }}"
          {{- end }}
    spec:
      serviceAccountName: kommisjon
      volumes:
        - name: customer-template
          configMap:
            name: customer-template
      containers:
        - name: kommisjon-slack-bot
          image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/kommisjon-slack-bot:latest
          imagePullPolicy: Always
          volumeMounts:
            - name: customer-template
              mountPath: /app/templates/customer.j2
              subPath: customer.j2
          env:
            - name: LOGURU_LEVEL
              value: WARNING
            - name: SLACK_CHANNEL_ID
              value: C020GFWC73Q # server-provisioning
            - name: AWS_S3_BUCKET_NAME
              value: kommisjon-nodes-ap-southeast-2-************ #cv-prod
            - name: AWS_IAM_ROLE_NAME
              value: arn:aws:iam::************:role/KommisjonSlackBot
          command: 
            ["/bin/bash", "-c"]
          args: 
            ["source /vault/secrets/config && python app.py"]

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: customer-template
  namespace: kommisjon
data: 
  customer.j2: |
    {
        "type": "input",
        "label": {
            "type": "plain_text",
            "text": "Customer"
        },
        "element": {
            "type": "static_select",
            "action_id": "customer",
            "placeholder": {
                "type": "plain_text",
                "text": "Select"
            },
            "options": [
                {
                    "text": {
                        "type": "plain_text",
                        "text": "BKG - Burger King"
                    },
                    "value": "bkg"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "CFA - Chick-Fil-A"
                    },
                    "value": "cfa"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "CUL - Culvers"
                    },
                    "value": "cul"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "CZP - Cafe Zupas"
                    },
                    "value": "czp"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "ELJ - El Jannah"
                    },
                    "value": "elj"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "KFC - Kentucky Fried Chicken"
                    },
                    "value": "kfc"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "MCD - McDonalds"
                    },
                    "value": "mcd"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "POC - Proof of Concept"
                    },
                    "value": "poc"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "POP - Popeyes"
                    },
                    "value": "pop"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "PTL - Portillos"
                    },
                    "value": "ptl"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "STB - Starbucks"
                    },
                    "value": "stb"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "Test"
                    },
                    "value": "tst"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "TIM - Tim Hortons"
                    },
                    "value": "tim"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "ZMB - Zambrero"
                    },
                    "value": "zmb"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "DEV NZL - New Zealand Dev"
                    },
                    "value": "dev-nzl"
                },
                {
                    "text": {
                        "type": "plain_text",
                        "text": "STG NZL - New Zealand Staging"
                    },
                    "value": "stg-nzl"
                }
            ]
        }
    }