terraform {
  required_version = "~> 1.10.0"

  backend "s3" {
    encrypt        = true
    bucket         = "fingermark-terraform"
    region         = "ap-southeast-2"
    key            = "./infra-k8s/atlantis/terraform.tfstate"
    dynamodb_table = "terraform-state"
    assume_role = {
      role_arn       = "arn:aws:iam::055313672806:role/TerraformBackendAccess"
      session_name   = "default"
    }
  }
  required_providers {
    local = {
      version = "~> 2.1"
    }
  }
}
