resource "aws_security_group" "eks_atlantis_security_group" {
  name        = "eks-atlantis"
  description = "Allow bitbucket Ips onto atlantis"
  vpc_id      = var.atlantis_alb_vpc
  ingress = [
    {
      description      = "Allow incoming connections from trusted locations"
      from_port        = 443
      to_port          = 443
      protocol         = "TCP"
      self             = true
      cidr_blocks      = var.bitbucket_cidr_blocks
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    },
    {
      description      = "Allow incoming connections from trusted locations"
      from_port        = 80
      to_port          = 80
      protocol         = "TCP"
      self             = true
      cidr_blocks      = var.bitbucket_cidr_blocks
      prefix_list_ids  = null
      ipv6_cidr_blocks = null
      security_groups  = null
    }
  ]
  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }
  tags = var.tags
}
