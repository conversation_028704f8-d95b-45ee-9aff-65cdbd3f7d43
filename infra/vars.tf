variable "bitbucket_cidr_blocks" {
  description = "Current Bitbucket Cloud IP ranges for webhooks (updated Dec 2024)"
  default = [
    # Current IPv4 ranges for webhook delivery from Atlassian official documentation
    # Source: https://support.atlassian.com/bitbucket-cloud/docs/what-are-the-bitbucket-cloud-ip-addresses-i-should-use-to-configure-my-corporate-firewall/

    # Primary ranges (current as of Dec 2024)
    "*************/21",
    "*************/22",
    "*************/25",

    # Individual ranges for webhook delivery (from Atlassian outgoing connections)
    "**********/28",
    "*************/28",
    "************/28",
    "************/28",
    "*************/28",
    "*************/28",
    "*************/28",
    "*************/28",
    "************/28",
    "**************/28",
    "***************/28",
    "***************/28",
    "***************/28",
    "***************/28",
    "***************/28",
    "***************/28",
    "***************/28",
    "***************/28",
    "***************/28",
    "***************/28"
  ]
}
